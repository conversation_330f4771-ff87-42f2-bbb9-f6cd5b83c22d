<?php $__env->startSection('content'); ?>
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3><?php echo e(__('plugins/chatbase::chatbase.dashboard.title')); ?></h3>
        </div>
        <div class="ps-section__content">
            <?php if(!$agent): ?>
                <div class="alert alert-info">
                    <h4 class="alert-heading"><?php echo e(__('plugins/chatbase::chatbase.dashboard.no_agent')); ?></h4>
                    <p><?php echo e(__('plugins/chatbase::chatbase.dashboard.no_agent_description')); ?></p>
                    <hr>
                    <a href="<?php echo e(route('marketplace.vendor.chatbase.create')); ?>" class="btn btn-primary">
                        <i class="fa fa-plus"></i>
                        <?php echo e(__('plugins/chatbase::chatbase.dashboard.create_agent')); ?>

                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><?php echo e(__('plugins/chatbase::chatbase.dashboard.agent_info')); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong><?php echo e(__('Name')); ?>:</strong>
                                        <p><?php echo e($agent->name); ?></p>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong><?php echo e(__('plugins/chatbase::chatbase.dashboard.agent_status')); ?>:</strong>
                                        <p>
                                            <?php if($agent->status === 'active'): ?>
                                                <span class="badge bg-success"><?php echo e(__('plugins/chatbase::chatbase.agent.status.active')); ?></span>
                                            <?php elseif($agent->status === 'creating'): ?>
                                                <span class="badge bg-warning"><?php echo e(__('plugins/chatbase::chatbase.agent.status.creating')); ?></span>
                                            <?php elseif($agent->status === 'error'): ?>
                                                <span class="badge bg-danger"><?php echo e(__('plugins/chatbase::chatbase.agent.status.error')); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo e(__('plugins/chatbase::chatbase.agent.status.draft')); ?></span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>

                                <?php if($agent->description): ?>
                                    <div class="row">
                                        <div class="col-12">
                                            <strong><?php echo e(__('Description')); ?>:</strong>
                                            <p><?php echo e($agent->description); ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if($agent->chatbot_id): ?>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong><?php echo e(__('plugins/chatbase::chatbase.dashboard.chatbot_id')); ?>:</strong>
                                            <p><code><?php echo e($agent->chatbot_id); ?></code></p>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong><?php echo e(__('plugins/chatbase::chatbase.dashboard.widget_status')); ?>:</strong>
                                            <p>
                                                <?php if($agent->getSettingValue('widget.enabled', true)): ?>
                                                    <span class="badge bg-success"><?php echo e(__('plugins/chatbase::chatbase.dashboard.widget_enabled')); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?php echo e(__('plugins/chatbase::chatbase.dashboard.widget_disabled')); ?></span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if($agent->hasError() && $agent->error_message): ?>
                                    <div class="row">
                                        <div class="col-12">
                                            <strong><?php echo e(__('plugins/chatbase::chatbase.dashboard.error_message')); ?>:</strong>
                                            <div class="alert alert-danger"><?php echo e($agent->error_message); ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>


                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><?php echo e(__('plugins/chatbase::chatbase.dashboard.manage_agent')); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="<?php echo e(route('marketplace.vendor.chatbase.edit', $agent->id)); ?>" class="btn btn-primary">
                                        <i class="fa fa-edit"></i>
                                        <?php echo e(__('plugins/chatbase::chatbase.dashboard.edit_agent')); ?>

                                    </a>

                                    <a href="<?php echo e(route('marketplace.vendor.chatbase.settings', $agent->id)); ?>" class="btn btn-info">
                                        <i class="fa fa-cog"></i>
                                        <?php echo e(__('plugins/chatbase::chatbase.dashboard.chatbot_settings')); ?>

                                    </a>


                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAgentModal">
                                        <i class="fa fa-trash"></i>
                                        <?php echo e(__('plugins/chatbase::chatbase.dashboard.delete_agent')); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div class="modal fade" id="deleteAgentModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><?php echo e(__('Confirm Deletion')); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p><?php echo e(__('Are you sure you want to delete this agent? This action cannot be undone.')); ?></p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                                <form method="POST" action="<?php echo e(route('marketplace.vendor.chatbase.destroy', $agent->id)); ?>" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger"><?php echo e(__('Delete')); ?></button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laragon\www\muhrak\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/index.blade.php ENDPATH**/ ?>