<?php

namespace Botble\Chatbase\Http\Controllers\Admin;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\Admin\AdminChatbotForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\Admin\AdminChatbotRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseAgentService;
use Botble\Chatbase\Tables\ChatbotTable;
use Illuminate\Http\Request;

class ChatbotController extends BaseController
{
    public function index(ChatbotTable $table)
    {
        $this->pageTitle(trans('plugins/chatbase::chatbase.admin.chatbots.title'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/chatbase::chatbase.admin.chatbots.create'));

        return AdminChatbotForm::create()->renderForm();
    }

    public function store(AdminChatbotRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $data = $request->validated();
        
        // Get store from the request
        $store = \Botble\Marketplace\Models\Store::findOrFail($data['store_id']);
        
        $result = $agentService->createAgentForStore($store, $data);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('chatbase.admin.chatbots.index'))
            ->setMessage(trans('plugins/chatbase::chatbase.admin.chatbots.created'));
    }

    public function show(string $id)
    {
        $agent = ChatbaseAgent::findOrFail($id);
        
        $this->pageTitle(trans('plugins/chatbase::chatbase.admin.chatbots.view', ['name' => $agent->name]));

        return view('plugins/chatbase::admin.chatbots.show', compact('agent'));
    }

    public function edit(string $id)
    {
        $agent = ChatbaseAgent::findOrFail($id);
        
        $this->pageTitle(trans('plugins/chatbase::chatbase.admin.chatbots.edit', ['name' => $agent->name]));

        return AdminChatbotForm::createFromModel($agent)
            ->setUrl(route('chatbase.admin.chatbots.update', $agent->id))
            ->renderForm();
    }

    public function update(string $id, AdminChatbotRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $agent = ChatbaseAgent::findOrFail($id);
        
        $result = $agentService->updateAgent($agent, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('chatbase.admin.chatbots.index'))
            ->setMessage(trans('plugins/chatbase::chatbase.admin.chatbots.updated'));
    }

    public function destroy(string $id, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $agent = ChatbaseAgent::findOrFail($id);
        
        $result = $agentService->deleteAgent($agent);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setMessage(trans('plugins/chatbase::chatbase.admin.chatbots.deleted'));
    }

    public function bulkActions(Request $request): BaseHttpResponse
    {
        $ids = $request->input('ids');
        $action = $request->input('action');

        if (empty($ids) || empty($action)) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(trans('core/base::forms.invalid_action'));
        }

        switch ($action) {
            case 'delete':
                $agentService = app(ChatbaseAgentService::class);
                $deleted = 0;
                
                foreach ($ids as $id) {
                    $agent = ChatbaseAgent::find($id);
                    if ($agent) {
                        $result = $agentService->deleteAgent($agent);
                        if ($result['success']) {
                            $deleted++;
                        }
                    }
                }

                return $this
                    ->httpResponse()
                    ->setMessage(trans('plugins/chatbase::chatbase.admin.chatbots.bulk_deleted', ['count' => $deleted]));

            default:
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage(trans('core/base::forms.invalid_action'));
        }
    }
}
