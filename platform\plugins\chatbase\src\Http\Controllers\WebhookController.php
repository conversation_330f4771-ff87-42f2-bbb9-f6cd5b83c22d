<?php

namespace Botble\Chatbase\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Chatbase\Models\ChatbaseAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends BaseController
{
    public function handle(Request $request): BaseHttpResponse
    {
        try {
            $payload = $request->all();
            
            Log::info('Chatbase webhook received', $payload);

            // Verify webhook signature if configured
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('Invalid webhook signature from Chatbase');
                return $this->httpResponse()->setError()->setMessage('Invalid signature');
            }

            $eventType = $payload['event'] ?? null;
            $chatbotId = $payload['chatbotId'] ?? null;

            if (!$chatbotId) {
                Log::warning('Webhook missing chatbotId');
                return $this->httpResponse()->setError()->setMessage('Missing chatbotId');
            }

            // Find the agent
            $agent = ChatbaseAgent::where('chatbot_id', $chatbotId)->first();
            if (!$agent) {
                Log::warning('Agent not found for chatbotId: ' . $chatbotId);
                return $this->httpResponse()->setError()->setMessage('Agent not found');
            }

            // Handle different event types
            switch ($eventType) {
                case 'conversation.started':
                    $this->handleConversationStarted($agent, $payload);
                    break;
                    
                case 'conversation.ended':
                    $this->handleConversationEnded($agent, $payload);
                    break;
                    
                case 'lead.captured':
                    $this->handleLeadCaptured($agent, $payload);
                    break;
                    
                case 'message.sent':
                    $this->handleMessageSent($agent, $payload);
                    break;
                    
                default:
                    Log::info('Unhandled webhook event type: ' . $eventType);
            }

            return $this->httpResponse()->setMessage('Webhook processed successfully');

        } catch (\Exception $e) {
            Log::error('Error processing Chatbase webhook: ' . $e->getMessage(), [
                'payload' => $request->all(),
                'exception' => $e,
            ]);

            return $this->httpResponse()->setError()->setMessage('Webhook processing failed');
        }
    }

    protected function verifyWebhookSignature(Request $request): bool
    {
        // Implement webhook signature verification if Chatbase provides it
        // For now, we'll just return true
        return true;
    }

    protected function handleConversationStarted(ChatbaseAgent $agent, array $payload): void
    {
        Log::info('Conversation started for agent: ' . $agent->name, $payload);
        
        // You could implement custom logic here, such as:
        // - Sending notifications to the vendor
        // - Updating statistics
        // - Triggering other actions
    }

    protected function handleConversationEnded(ChatbaseAgent $agent, array $payload): void
    {
        Log::info('Conversation ended for agent: ' . $agent->name, $payload);
        
        // You could implement custom logic here, such as:
        // - Updating conversation statistics
        // - Sending follow-up emails
        // - Analyzing conversation quality
    }

    protected function handleLeadCaptured(ChatbaseAgent $agent, array $payload): void
    {
        Log::info('Lead captured for agent: ' . $agent->name, $payload);
        
        // You could implement custom logic here, such as:
        // - Adding lead to CRM
        // - Sending notification to vendor
        // - Triggering email sequences
        
        $leadData = $payload['lead'] ?? [];
        
        if (!empty($leadData)) {
            // Example: Send notification to store owner
            $this->notifyStoreOwnerOfNewLead($agent, $leadData);
        }
    }

    protected function handleMessageSent(ChatbaseAgent $agent, array $payload): void
    {
        Log::info('Message sent for agent: ' . $agent->name, $payload);
        
        // You could implement custom logic here, such as:
        // - Updating message statistics
        // - Monitoring for specific keywords
        // - Quality assurance checks
    }

    protected function notifyStoreOwnerOfNewLead(ChatbaseAgent $agent, array $leadData): void
    {
        try {
            // Example implementation - you could send email, SMS, or in-app notification
            $store = $agent->store;
            $customer = $agent->customer;
            
            if ($customer && $customer->email) {
                // Here you would implement your notification logic
                // For example, using Laravel's notification system:
                // $customer->notify(new NewLeadNotification($leadData, $agent));
                
                Log::info('Lead notification sent to: ' . $customer->email, [
                    'agent_id' => $agent->id,
                    'lead_data' => $leadData,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to notify store owner of new lead: ' . $e->getMessage());
        }
    }
}
