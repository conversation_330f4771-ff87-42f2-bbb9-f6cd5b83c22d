<?php

return [
    'name' => 'Chatbase Integration',
    'description' => 'Integrate Chatbase.co AI agents for vendor stores',

    'settings' => [
        'title' => 'Chatbase Integration',
        'description' => 'Configure Chatbase.co AI agents for vendor stores with API key management and widget settings',
        'api_key' => 'Chatbase API Key',
        'api_key_helper' => 'Enter your Chatbase API key. You can find this in your Chatbase dashboard under Settings > API Keys.',
        'api_key_invalid' => 'The provided API key is invalid. Error: :error',
        'widget_enabled' => 'Enable Chatbase Widget',
        'widget_enabled_helper' => 'Allow vendors to embed Chatbase widgets on their store pages',
        'auto_embed_widget' => 'Auto-embed Widget',
        'auto_embed_widget_helper' => 'Automatically embed the Chatbase widget on store pages when agents are active',
        'default_model' => 'Default AI Model',
        'default_model_helper' => 'The default AI model to use for new agents',
        'widget_position' => 'Default Widget Position',
        'widget_position_helper' => 'Default position for the chat widget on store pages',
        'widget_theme' => 'Default Widget Theme',
        'widget_theme_helper' => 'Default theme for the chat widget',
        'default_instructions' => 'Default Instructions',
        'default_instructions_helper' => 'Default instructions that will be added to all new agents',
        'default_instructions_placeholder' => 'You are a helpful assistant for this store. Be polite and provide accurate information about products and services.',

        'position_bottom_right' => 'Bottom Right',
        'position_bottom_left' => 'Bottom Left',
        'position_top_right' => 'Top Right',
        'position_top_left' => 'Top Left',

        'theme_light' => 'Light',
        'theme_dark' => 'Dark',
        'theme_auto' => 'Auto (System)',
    ],

    'agent' => [
        'name' => 'Agent Name',
        'name_helper' => 'A descriptive name for your AI agent',
        'name_placeholder' => 'e.g., Store Assistant, Customer Support Bot',
        'description' => 'Description',
        'description_helper' => 'Brief description of what your agent does',
        'description_placeholder' => 'This agent helps customers with product inquiries and store information',
        'training_text' => 'Training Content',
        'training_text_helper' => 'Provide information about your store, products, policies, etc. This will help train your agent. WARNING: This will replace ALL existing training data on Chatbase with this new content.',
        'training_text_placeholder' => 'Enter information about your store, products, services, policies, FAQ, etc.',
        'model' => 'AI Model',
        'model_helper' => 'Choose the AI model for your agent. GPT-4o Mini is recommended for most use cases.',
        'temperature' => 'Creativity Level',
        'temperature_helper' => 'Controls how creative vs focused the agent responses are',
        'temperature_focused' => 'Focused & Consistent',
        'temperature_balanced' => 'Balanced',
        'temperature_creative' => 'Creative',
        'temperature_very_creative' => 'Very Creative',
        'widget_enabled' => 'Enable Widget',
        'widget_enabled_helper' => 'Show the chat widget on your store page',
        'widget_position' => 'Widget Position',
        'widget_position_helper' => 'Where to position the chat widget on your store page',
        'widget_theme' => 'Widget Theme',
        'widget_theme_helper' => 'Visual theme for the chat widget',
        'custom_instructions' => 'Custom Instructions',
        'custom_instructions_helper' => 'Additional instructions to customize your agent behavior',
        'custom_instructions_placeholder' => 'Always mention our free shipping policy. Be friendly and helpful.',

        'instructions' => 'Base Instructions',
        'instructions_helper' => 'Base prompt guiding chatbot behavior and personality',
        'instructions_placeholder' => 'You are a helpful customer service assistant for this store...',

        'initial_messages' => 'Initial Messages',
        'initial_messages_helper' => 'Messages the chatbot uses to greet users (one per line)',
        'initial_messages_placeholder' => 'Hello! How can I help you today?',

        'suggested_messages' => 'Suggested Messages',
        'suggested_messages_helper' => 'Suggested prompts displayed above the chat input (one per line)',
        'suggested_messages_placeholder' => 'What are your shipping policies?',

        'visibility' => 'Visibility',
        'visibility_helper' => 'Set chatbot visibility to public or private',
        'visibility_private' => 'Private',
        'visibility_public' => 'Public',

        'only_allow_on_added_domains' => 'Restrict to Specific Domains',
        'only_allow_on_added_domains_helper' => 'Only allow chatbot usage on specified domains',

        'domains' => 'Allowed Domains',
        'domains_helper' => 'List of domains where chatbot is allowed (one per line)',
        'domains_placeholder' => 'example.com',

        'ip_limit' => 'IP Rate Limit',
        'ip_limit_helper' => 'Maximum messages allowed per IP within timeframe',
        'ip_limit_placeholder' => '100',

        'ip_limit_timeframe' => 'IP Limit Timeframe (seconds)',
        'ip_limit_timeframe_helper' => 'Timeframe in seconds for IP rate limiting',
        'ip_limit_timeframe_placeholder' => '3600',

        'ip_limit_message' => 'IP Limit Message',
        'ip_limit_message_helper' => 'Message displayed when IP limit is exceeded',
        'ip_limit_message_placeholder' => 'Rate limit exceeded. Please try again later.',

        'temp' => 'Temperature',
        'temp_helper' => 'Controls response randomness (0 = focused, 1 = creative)',
        'temp_focused' => 'Focused & Consistent',
        'temp_balanced' => 'Balanced',
        'temp_creative' => 'Creative',
        'temp_very_creative' => 'Very Creative',

        'status' => [
            'draft' => 'Draft',
            'creating' => 'Creating...',
            'active' => 'Active',
            'error' => 'Error',
        ],
    ],

    'dashboard' => [
        'title' => 'Chatbase Agent',
        'no_agent' => 'No Agent Created',
        'no_agent_description' => 'You haven\'t created a Chatbase agent yet. Create one to provide AI-powered customer support on your store.',
        'create_agent' => 'Create Agent',
        'agent_status' => 'Agent Status',
        'agent_info' => 'Agent Information',
        'widget_status' => 'Widget Status',
        'training_sources' => 'Training Sources',
        'manage_agent' => 'Manage Agent',
        'edit_agent' => 'Edit Agent',
        'chatbot_settings' => 'Chatbot Settings',
        'train_agent' => 'Train Agent',
        'delete_agent' => 'Delete Agent',
        'view_analytics' => 'View Analytics',
        'widget_enabled' => 'Widget Enabled',
        'widget_disabled' => 'Widget Disabled',
        'total_characters' => 'Total Training Characters',
        'last_trained' => 'Last Trained',
        'never' => 'Never',
        'chatbot_id' => 'Chatbot ID',
        'error_message' => 'Error Message',
    ],

    'training' => [
        'title' => 'Train Your Agent',
        'description' => 'Add content to train your AI agent. The more relevant information you provide, the better your agent will perform.',
        'add_source' => 'Add Training Source',
        'source_type' => 'Source Type',
        'source_title' => 'Title',
        'source_content' => 'Content',
        'source_url' => 'URL',
        'source_file' => 'File',
        'character_count' => 'Characters',
        'no_sources' => 'No training sources added yet',
        'types' => [
            'text' => 'Text Content',
            'url' => 'Website URL',
            'file' => 'File Upload',
        ],
    ],

    'widget' => [
        'loading' => 'Loading chat...',
        'error' => 'Chat temporarily unavailable',
    ],

    'messages' => [
        'agent_created' => 'Agent created successfully!',
        'agent_updated' => 'Agent updated successfully!',
        'agent_deleted' => 'Agent deleted successfully!',
        'agent_exists' => 'Agent already exists for your store',
        'api_key_required' => 'Chatbase API key is required. Please contact administrator.',
        'creation_failed' => 'Failed to create agent. Please try again.',
        'update_failed' => 'Failed to update agent. Please try again.',
        'delete_failed' => 'Failed to delete agent. Please try again.',
    ],
];
