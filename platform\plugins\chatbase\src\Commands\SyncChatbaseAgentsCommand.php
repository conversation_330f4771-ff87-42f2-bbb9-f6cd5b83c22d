<?php

namespace Botble\Chatbase\Commands;

use <PERSON><PERSON><PERSON>\Chatbase\Models\ChatbaseAgent;
use <PERSON><PERSON>ble\Chatbase\Services\ChatbaseApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncChatbaseAgentsCommand extends Command
{
    protected $signature = 'chatbase:sync-agents 
                           {--force : Force sync even if recently synced}
                           {--agent= : Sync specific agent by ID}';

    protected $description = 'Sync local Chatbase agents with Chatbase API';

    public function handle(ChatbaseApiService $apiService): int
    {
        $this->info('Starting Chatbase agents sync...');

        $query = ChatbaseAgent::query();

        // Filter by specific agent if provided
        if ($agentId = $this->option('agent')) {
            $query->where('id', $agentId);
        }

        // Skip recently synced agents unless forced
        if (!$this->option('force')) {
            $query->where(function ($q) {
                $q->whereNull('last_synced_at')
                  ->orWhere('last_synced_at', '<', now()->subHours(1));
            });
        }

        $agents = $query->get();

        if ($agents->isEmpty()) {
            $this->info('No agents to sync.');
            return self::SUCCESS;
        }

        $this->info("Found {$agents->count()} agents to sync.");

        $synced = 0;
        $errors = 0;

        foreach ($agents as $agent) {
            $this->line("Syncing agent: {$agent->name} (ID: {$agent->id})");

            try {
                if ($this->syncAgent($apiService, $agent)) {
                    $synced++;
                    $this->info("✓ Synced: {$agent->name}");
                } else {
                    $errors++;
                    $this->error("✗ Failed: {$agent->name}");
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Error syncing {$agent->name}: " . $e->getMessage());
                Log::error('Error syncing Chatbase agent', [
                    'agent_id' => $agent->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->info("\nSync completed:");
        $this->info("✓ Synced: {$synced}");
        $this->info("✗ Errors: {$errors}");

        return $errors > 0 ? self::FAILURE : self::SUCCESS;
    }

    protected function syncAgent(ChatbaseApiService $apiService, ChatbaseAgent $agent): bool
    {
        // If agent doesn't have a chatbot_id, skip it
        if (!$agent->chatbot_id) {
            $this->warn("Agent {$agent->name} has no chatbot_id, skipping.");
            return false;
        }

        // Get chatbot info from Chatbase API
        $response = $apiService->getChatbots();

        if (!$response['success']) {
            $this->error("Failed to get chatbots from API: " . $response['error']);
            return false;
        }

        $chatbots = $response['data']['chatbots'] ?? [];
        $chatbot = collect($chatbots)->firstWhere('id', $agent->chatbot_id);

        if (!$chatbot) {
            $this->warn("Chatbot {$agent->chatbot_id} not found in API response.");
            
            // Mark agent as error if chatbot doesn't exist
            $agent->update([
                'status' => 'error',
                'error_message' => 'Chatbot not found in Chatbase account',
                'last_synced_at' => now(),
            ]);
            
            return false;
        }

        // Update local agent with API data
        $updates = [
            'last_synced_at' => now(),
        ];

        // Check if chatbot is active
        if (isset($chatbot['status'])) {
            $apiStatus = $chatbot['status'];
            $localStatus = $this->mapApiStatusToLocal($apiStatus);
            
            if ($agent->status !== $localStatus) {
                $updates['status'] = $localStatus;
                $this->info("Status updated: {$agent->status} → {$localStatus}");
            }
        }

        // Clear error message if chatbot is working
        if ($agent->status === 'error' && isset($chatbot['status']) && $chatbot['status'] === 'active') {
            $updates['error_message'] = null;
        }

        $agent->update($updates);

        return true;
    }

    protected function mapApiStatusToLocal(string $apiStatus): string
    {
        return match ($apiStatus) {
            'active', 'published' => 'active',
            'training', 'processing' => 'creating',
            'error', 'failed' => 'error',
            default => 'draft',
        };
    }
}
