<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('chatbase_training_sources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agent_id')->constrained('chatbase_agents')->onDelete('cascade');
            $table->string('type'); // text, url, file
            $table->string('title');
            $table->longText('content')->nullable();
            $table->string('url')->nullable();
            $table->string('file_path')->nullable();
            $table->string('status')->default('active'); // active, inactive
            $table->integer('character_count')->default(0);
            $table->timestamps();

            $table->index(['agent_id', 'type']);
            $table->index(['agent_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('chatbase_training_sources');
    }
};
