<?php

namespace Botble\Chatbase\Services;

use Bo<PERSON><PERSON>\Chatbase\Jobs\CreateChatbaseAgentJob;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseTrainingSource;
use Botble\Marketplace\Models\Store;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChatbaseAgentService
{
    protected ChatbaseApiService $apiService;

    public function __construct(ChatbaseApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    public function createAgentForStore(Store $store, array $data, bool $async = false): array
    {
        try {
            DB::beginTransaction();

            // Check if agent already exists for this store
            $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
            if ($existingAgent) {
                return [
                    'success' => false,
                    'error' => 'Agent already exists for this store',
                ];
            }

            // Create local agent record
            $agent = ChatbaseAgent::create([
                'name' => $data['name'] ?? $store->name . ' Assistant',
                'description' => $data['description'] ?? 'AI assistant for ' . $store->name,
                'store_id' => $store->id,
                'customer_id' => $store->customer_id,
                'status' => 'creating',
                'settings' => $data['settings'] ?? [],
            ]);

            // Prepare training data
            $trainingText = $this->prepareTrainingData($store, $data);

            if ($async) {
                // Create agent asynchronously using job
                CreateChatbaseAgentJob::dispatch($agent, $trainingText);

                // Agent status remains 'creating' until job completes
                $chatbotId = null;
            } else {
                // Create chatbot via API synchronously
                $apiResponse = $this->apiService->createChatbot(
                    $agent->name,
                    $trainingText
                );

                if (!$apiResponse['success']) {
                    $agent->update([
                        'status' => 'error',
                        'error_message' => $apiResponse['error'],
                    ]);

                    DB::rollBack();
                    return $apiResponse;
                }

                // Update agent with chatbot ID
                $agent->update([
                    'chatbot_id' => $apiResponse['data']['chatbotId'],
                    'status' => 'active',
                    'last_trained_at' => now(),
                    'last_synced_at' => now(),
                ]);

                $chatbotId = $apiResponse['data']['chatbotId'];
            }

            // Create training sources if provided
            if (!empty($data['training_sources'])) {
                $this->createTrainingSources($agent, $data['training_sources']);
            }

            DB::commit();

            return [
                'success' => true,
                'agent' => $agent,
                'chatbot_id' => $chatbotId,
                'async' => $async,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to create agent: ' . $e->getMessage(),
            ];
        }
    }

    public function updateAgent(ChatbaseAgent $agent, array $data): array
    {
        try {
            DB::beginTransaction();

            // Update local agent
            $agent->update([
                'name' => $data['name'] ?? $agent->name,
                'description' => $data['description'] ?? $agent->description,
                'settings' => array_merge($agent->settings ?? [], $data['settings'] ?? []),
            ]);

            // Update via API if chatbot exists
            if ($agent->chatbot_id) {
                $apiData = [];

                if (isset($data['name'])) {
                    $apiData['chatbotName'] = $data['name'];
                }

                // If training text is provided, update the chatbot data
                if (!empty($data['training_text'])) {
                    $apiResponse = $this->apiService->updateChatbotData(
                        $agent->chatbot_id,
                        $data['name'] ?? $agent->name,
                        $data['training_text']
                    );

                    if (!$apiResponse['success']) {
                        DB::rollBack();
                        return $apiResponse;
                    }

                    // Update last trained timestamp
                    $agent->update(['last_trained_at' => now()]);
                }

                // If API settings are provided, update chatbot settings
                if (!empty($data['api_settings'])) {
                    $apiSettings = $data['api_settings'];
                    $apiSettings['chatbotId'] = $agent->chatbot_id;

                    $apiResponse = $this->apiService->updateChatbotSettings($agent->chatbot_id, $apiSettings);

                    if (!$apiResponse['success']) {
                        DB::rollBack();
                        return $apiResponse;
                    }
                } elseif (!empty($apiData)) {
                    // Only call updateChatbot if we have other data to update
                    $apiResponse = $this->apiService->updateChatbot($agent->chatbot_id, $apiData);

                    if (!$apiResponse['success']) {
                        DB::rollBack();
                        return $apiResponse;
                    }
                }

                $agent->update(['last_synced_at' => now()]);
            }

            DB::commit();

            return [
                'success' => true,
                'agent' => $agent->fresh(),
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to update agent: ' . $e->getMessage(),
            ];
        }
    }

    public function updateAgentSettings(ChatbaseAgent $agent, array $data): array
    {
        try {
            DB::beginTransaction();

            // Update local agent settings
            $updateData = [];

            if (isset($data['instructions'])) {
                $updateData['instructions'] = $data['instructions'];
            }
            if (isset($data['initial_messages'])) {
                $updateData['initial_messages'] = $this->parseArrayField($data['initial_messages']);
            }
            if (isset($data['suggested_messages'])) {
                $updateData['suggested_messages'] = $this->parseArrayField($data['suggested_messages']);
            }
            if (isset($data['visibility'])) {
                $updateData['visibility'] = $data['visibility'];
            }
            if (isset($data['only_allow_on_added_domains'])) {
                $updateData['only_allow_on_added_domains'] = $data['only_allow_on_added_domains'];
            }
            if (isset($data['domains'])) {
                $updateData['domains'] = $this->parseArrayField($data['domains']);
            }
            if (isset($data['ip_limit'])) {
                $updateData['ip_limit'] = $data['ip_limit'];
            }
            if (isset($data['ip_limit_timeframe'])) {
                $updateData['ip_limit_timeframe'] = $data['ip_limit_timeframe'];
            }
            if (isset($data['ip_limit_message'])) {
                $updateData['ip_limit_message'] = $data['ip_limit_message'];
            }
            if (isset($data['model'])) {
                $updateData['model'] = $data['model'];
            }
            if (isset($data['temp'])) {
                $updateData['temp'] = (float) $data['temp'];
            }

            if (!empty($updateData)) {
                $agent->update($updateData);
            }

            // Update via API if chatbot exists and API settings are provided
            if ($agent->chatbot_id && !empty($data['api_settings'])) {
                $apiSettings = $data['api_settings'];
                $apiSettings['chatbotId'] = $agent->chatbot_id;

                $apiResponse = $this->apiService->updateChatbotSettings($agent->chatbot_id, $apiSettings);

                if (!$apiResponse['success']) {
                    DB::rollBack();
                    return $apiResponse;
                }

                $agent->update(['last_synced_at' => now()]);
            }

            DB::commit();

            return [
                'success' => true,
                'agent' => $agent->fresh(),
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating Chatbase agent settings: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to update agent settings: ' . $e->getMessage(),
            ];
        }
    }

    public function deleteAgent(ChatbaseAgent $agent): array
    {
        try {
            DB::beginTransaction();

            // Delete from Chatbase API if chatbot exists
            if ($agent->chatbot_id) {
                $apiResponse = $this->apiService->deleteChatbot($agent->chatbot_id);

                if (!$apiResponse['success']) {
                    Log::warning('Failed to delete chatbot from Chatbase API: ' . $apiResponse['error']);
                    // Continue with local deletion even if API deletion fails
                }
            }

            // Delete local records
            $agent->trainingSources()->delete();
            $agent->delete();

            DB::commit();

            return [
                'success' => true,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to delete agent: ' . $e->getMessage(),
            ];
        }
    }

    protected function prepareTrainingData(Store $store, array $data): string
    {
        $trainingText = [];

        // Store basic information
        $trainingText[] = "Store Name: " . $store->name;
        $trainingText[] = "Store Description: " . ($store->description ?: 'No description available');

        if ($store->address) {
            $trainingText[] = "Store Address: " . $store->address;
        }

        if ($store->phone) {
            $trainingText[] = "Store Phone: " . $store->phone;
        }

        if ($store->email) {
            $trainingText[] = "Store Email: " . $store->email;
        }

        // Add custom training text if provided
        if (!empty($data['training_text'])) {
            $trainingText[] = "Additional Information: " . $data['training_text'];
        }

        return implode("\n\n", $trainingText);
    }

    protected function createTrainingSources(ChatbaseAgent $agent, array $sources): void
    {
        foreach ($sources as $source) {
            $trainingSource = ChatbaseTrainingSource::create([
                'agent_id' => $agent->id,
                'type' => $source['type'],
                'title' => $source['title'],
                'content' => $source['content'] ?? null,
                'url' => $source['url'] ?? null,
                'file_path' => $source['file_path'] ?? null,
                'status' => 'active',
            ]);

            $trainingSource->updateCharacterCount();
        }
    }

    protected function parseArrayField(string $field): array
    {
        if (empty($field)) {
            return [];
        }

        return array_filter(array_map('trim', explode("\n", $field)));
    }
}
