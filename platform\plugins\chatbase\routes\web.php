<?php

use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Botble\Chatbase\Http\Controllers\Settings\ChatbaseSettingController;
use Illuminate\Support\Facades\Route;

// Admin routes
AdminHelper::registerRoutes(function (): void {
    Route::group([
        'prefix' => 'chatbase',
        'as' => 'chatbase.',
        'permission' => 'chatbase.settings',
    ], function (): void {
        Route::get('settings', [ChatbaseSettingController::class, 'edit'])->name('settings');
        Route::put('settings', [ChatbaseSettingController::class, 'update'])->name('settings.update');
    });
});
