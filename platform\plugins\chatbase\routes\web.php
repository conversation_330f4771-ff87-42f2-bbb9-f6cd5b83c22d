<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Admin\ChatbotController as AdminChatbotController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Settings\ChatbaseSettingController;
use Illuminate\Support\Facades\Route;

// Admin routes
AdminHelper::registerRoutes(function (): void {
    Route::group([
        'prefix' => 'chatbase',
        'as' => 'chatbase.',
        'permission' => 'chatbase.settings',
    ], function (): void {
        Route::get('settings', [ChatbaseSettingController::class, 'edit'])->name('settings');
        Route::put('settings', [ChatbaseSettingController::class, 'update'])->name('settings.update');

        // Admin chatbot management
        Route::group(['prefix' => 'admin', 'as' => 'admin.'], function (): void {
            Route::resource('chatbots', AdminChatbotController::class)->names([
                'index' => 'chatbots.index',
                'create' => 'chatbots.create',
                'store' => 'chatbots.store',
                'show' => 'chatbots.show',
                'edit' => 'chatbots.edit',
                'update' => 'chatbots.update',
                'destroy' => 'chatbots.destroy',
            ]);
            Route::post('chatbots/bulk-actions', [AdminChatbotController::class, 'bulkActions'])->name('chatbots.bulk-actions');
        });
    });
});
