<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Admin\ChatbotController as AdminChatbotController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Settings\ChatbaseSettingController;
use Illuminate\Support\Facades\Route;

// Admin routes
AdminHelper::registerRoutes(function (): void {
    Route::group([
        'prefix' => 'chatbase',
        'as' => 'chatbase.',
        'permission' => 'chatbase.settings',
    ], function (): void {
        Route::get('settings', [ChatbaseSettingController::class, 'edit'])->name('settings');
        Route::put('settings', [ChatbaseSettingController::class, 'update'])->name('settings.update');

        // Admin chatbot management
        Route::group(['prefix' => 'admin', 'as' => 'admin.'], function (): void {
            Route::get('chatbots', [AdminChatbotController::class, 'index'])->name('chatbots.index');
            Route::get('chatbots/create', [AdminChatbotController::class, 'create'])->name('chatbots.create');
            Route::post('chatbots', [AdminChatbotController::class, 'store'])->name('chatbots.store');
            Route::get('chatbots/{id}', [AdminChatbotController::class, 'show'])->name('chatbots.show');
            Route::get('chatbots/{id}/edit', [AdminChatbotController::class, 'edit'])->name('chatbots.edit');
            Route::put('chatbots/{id}', [AdminChatbotController::class, 'update'])->name('chatbots.update');
            Route::delete('chatbots/{id}', [AdminChatbotController::class, 'destroy'])->name('chatbots.destroy');
        });
    });
});
