<?php

namespace Botble\Chatbase\Supports;

use Bo<PERSON><PERSON>\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Marketplace\Models\Store;

class ChatbaseHelper
{
    public static function isConfigured(): bool
    {
        return !empty(setting('chatbase_api_key'));
    }

    public static function isWidgetEnabled(): bool
    {
        return (bool) setting('chatbase_widget_enabled', true);
    }

    public static function isAutoEmbedEnabled(): bool
    {
        return (bool) setting('chatbase_auto_embed_widget', true);
    }

    public static function getDefaultModel(): string
    {
        return setting('chatbase_default_model', 'gpt-4o-mini');
    }

    public static function getDefaultInstructions(): string
    {
        return setting('chatbase_default_instructions', '');
    }

    public static function getAgentForStore(Store $store): ?ChatbaseAgent
    {
        return ChatbaseAgent::where('store_id', $store->id)->first();
    }

    public static function getActiveAgentForStore(Store $store): ?ChatbaseAgent
    {
        return ChatbaseAgent::where('store_id', $store->id)
            ->where('status', 'active')
            ->whereNotNull('chatbot_id')
            ->first();
    }

    public static function canCreateAgent(Store $store): bool
    {
        if (!self::isConfigured()) {
            return false;
        }

        // Check if agent already exists
        $existingAgent = self::getAgentForStore($store);
        return !$existingAgent;
    }

    public static function getWidgetScript(ChatbaseAgent $agent): string
    {
        if (!$agent->isActive() || !$agent->getSettingValue('widget.enabled', true)) {
            return '';
        }

        return $agent->getWidgetCode();
    }

    public static function getAvailableModels(): array
    {
        return [
            'gpt-4o-mini' => 'GPT-4o Mini (Recommended)',
            'gpt-4o' => 'GPT-4o',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4' => 'GPT-4',
            'claude-3-5-sonnet' => 'Claude 3.5 Sonnet',
            'claude-3-opus' => 'Claude 3 Opus',
            'claude-3-haiku' => 'Claude 3 Haiku',
            'gemini-1.5-pro' => 'Gemini 1.5 Pro',
            'gemini-1.5-flash' => 'Gemini 1.5 Flash',
        ];
    }

    public static function getWidgetPositions(): array
    {
        return [
            'bottom-right' => __('plugins/chatbase::chatbase.settings.position_bottom_right'),
            'bottom-left' => __('plugins/chatbase::chatbase.settings.position_bottom_left'),
            'top-right' => __('plugins/chatbase::chatbase.settings.position_top_right'),
            'top-left' => __('plugins/chatbase::chatbase.settings.position_top_left'),
        ];
    }

    public static function getWidgetThemes(): array
    {
        return [
            'light' => __('plugins/chatbase::chatbase.settings.theme_light'),
            'dark' => __('plugins/chatbase::chatbase.settings.theme_dark'),
            'auto' => __('plugins/chatbase::chatbase.settings.theme_auto'),
        ];
    }

    public static function getTemperatureOptions(): array
    {
        return [
            '0' => '0 - ' . __('plugins/chatbase::chatbase.agent.temperature_focused'),
            '0.3' => '0.3 - ' . __('plugins/chatbase::chatbase.agent.temperature_balanced'),
            '0.7' => '0.7 - ' . __('plugins/chatbase::chatbase.agent.temperature_creative'),
            '1' => '1 - ' . __('plugins/chatbase::chatbase.agent.temperature_very_creative'),
        ];
    }

    public static function formatCharacterCount(int $count): string
    {
        if ($count >= 1000000) {
            return number_format($count / 1000000, 1) . 'M';
        }

        if ($count >= 1000) {
            return number_format($count / 1000, 1) . 'K';
        }

        return number_format($count);
    }

    public static function getStatusBadgeClass(string $status): string
    {
        return match ($status) {
            'active' => 'bg-success',
            'creating' => 'bg-warning',
            'error' => 'bg-danger',
            default => 'bg-secondary',
        };
    }

    public static function getStatusIcon(string $status): string
    {
        return match ($status) {
            'active' => 'ti ti-check-circle',
            'creating' => 'ti ti-loader',
            'error' => 'ti ti-alert-circle',
            default => 'ti ti-circle',
        };
    }

    public static function getChatbaseUrl(string $path = ''): string
    {
        $baseUrl = 'https://www.chatbase.co';
        return $path ? $baseUrl . '/' . ltrim($path, '/') : $baseUrl;
    }

    public static function getAnalyticsUrl(string $chatbotId): string
    {
        return self::getChatbaseUrl("chatbot/{$chatbotId}/analytics");
    }

    public static function getDashboardUrl(string $chatbotId): string
    {
        return self::getChatbaseUrl("chatbot/{$chatbotId}");
    }
}
