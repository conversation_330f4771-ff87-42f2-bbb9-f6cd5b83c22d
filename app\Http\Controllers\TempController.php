<?php

namespace App\Http\Controllers;

use Botble\Base\Facades\BaseHelper;
use Botble\Ecommerce\Models\Product;
use Illuminate\Http\Response;

class TempController extends Controller
{
    public function importProductsFromCsv()
{
    // CSV file ka path
    $filePath = storage_path('app/products_export.csv');

    if (!file_exists($filePath)) {
        return 'CSV file not found!';
    }

    // Native PHP CSV reader use karte hain
    $handle = fopen($filePath, 'r');

    // Pehla row skip karo (header)
    $header = fgetcsv($handle);

    $batchSize = 500;
    $batch = [];

    while (($row = fgetcsv($handle)) !== false) {
        $batch[] = $row;

        if (count($batch) >= $batchSize) {
            $this->processCsvBatch($batch);
            $batch = [];
        }
    }

    // Last remaining rows process karo
    if (!empty($batch)) {
        $this->processCsvBatch($batch);
    }

    fclose($handle);

    return 'Products imported and updated successfully!';
}

private function processCsvBatch(array $rows)
{
    foreach ($rows as $row) {
        // CSV Columns: [0] => Name, [1] => Content
        $productName = $row[0];
        $productContent = $row[1];


        $product = Product::where('name', $productName)->first();
        if (!$product) {
            continue; // Not found, skip
        }
        $product->content = BaseHelper::clean($productContent);

        $product->save();
    }
}

}
