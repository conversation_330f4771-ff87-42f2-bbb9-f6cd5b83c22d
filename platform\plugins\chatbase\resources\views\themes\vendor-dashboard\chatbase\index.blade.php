@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3>{{ __('plugins/chatbase::chatbase.dashboard.title') }}</h3>
        </div>
        <div class="ps-section__content">
            @if(!$agent)
                <div class="alert alert-info">
                    <h4 class="alert-heading">{{ __('plugins/chatbase::chatbase.dashboard.no_agent') }}</h4>
                    <p>{{ __('plugins/chatbase::chatbase.dashboard.no_agent_description') }}</p>
                    <hr>
                    <a href="{{ route('marketplace.vendor.chatbase.create') }}" class="btn btn-primary">
                        <i class="fa fa-plus"></i>
                        {{ __('plugins/chatbase::chatbase.dashboard.create_agent') }}
                    </a>
                </div>
            @else
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.agent_info') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>{{ __('Name') }}:</strong>
                                        <p>{{ $agent->name }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>{{ __('plugins/chatbase::chatbase.dashboard.agent_status') }}:</strong>
                                        <p>
                                            @if($agent->status === 'active')
                                                <span class="badge bg-success">{{ __('plugins/chatbase::chatbase.agent.status.active') }}</span>
                                            @elseif($agent->status === 'creating')
                                                <span class="badge bg-warning">{{ __('plugins/chatbase::chatbase.agent.status.creating') }}</span>
                                            @elseif($agent->status === 'error')
                                                <span class="badge bg-danger">{{ __('plugins/chatbase::chatbase.agent.status.error') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('plugins/chatbase::chatbase.agent.status.draft') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>

                                @if($agent->description)
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>{{ __('Description') }}:</strong>
                                            <p>{{ $agent->description }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if($agent->chatbot_id)
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.chatbot_id') }}:</strong>
                                            <p><code>{{ $agent->chatbot_id }}</code></p>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.widget_status') }}:</strong>
                                            <p>
                                                @if($agent->getSettingValue('widget.enabled', true))
                                                    <span class="badge bg-success">{{ __('plugins/chatbase::chatbase.dashboard.widget_enabled') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('plugins/chatbase::chatbase.dashboard.widget_disabled') }}</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                @endif

                                @if($agent->hasError() && $agent->error_message)
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.error_message') }}:</strong>
                                            <div class="alert alert-danger">{{ $agent->error_message }}</div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>


                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.manage_agent') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('marketplace.vendor.chatbase.edit', $agent->id) }}" class="btn btn-primary">
                                        <i class="fa fa-edit"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.edit_agent') }}
                                    </a>

                                    <a href="{{ route('marketplace.vendor.chatbase.settings', $agent->id) }}" class="btn btn-info">
                                        <i class="fa fa-cog"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.chatbot_settings') }}
                                    </a>
{{--
                                    @if($agent->chatbot_id)
                                        <a href="{{ route('marketplace.vendor.chatbase.analytics', $agent->id) }}" class="btn btn-success">
                                            <i class="fa fa-chart-line"></i>
                                            {{ __('plugins/chatbase::chatbase.dashboard.view_analytics') }}
                                        </a>
                                    @endif --}}

                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAgentModal">
                                        <i class="fa fa-trash"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.delete_agent') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div class="modal fade" id="deleteAgentModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">{{ __('Confirm Deletion') }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>{{ __('Are you sure you want to delete this agent? This action cannot be undone.') }}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                                <form method="POST" action="{{ route('marketplace.vendor.chatbase.destroy', $agent->id) }}" style="display: inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">{{ __('Delete') }}</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
