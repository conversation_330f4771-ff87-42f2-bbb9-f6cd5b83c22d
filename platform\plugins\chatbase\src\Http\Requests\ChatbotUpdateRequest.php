<?php

namespace Botble\Chatbase\Http\Requests;

use Botble\Support\Http\Requests\Request;

class ChatbotUpdateRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'training_text' => ['nullable', 'string', 'max:50000'],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('plugins/chatbase::chatbase.agent.name'),
            'description' => trans('plugins/chatbase::chatbase.agent.description'),
            'training_text' => trans('plugins/chatbase::chatbase.agent.training_text'),
        ];
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // Prepare settings for storage
        $settings = [
            'model' => setting('chatbase_default_model', 'gpt-4o-mini'),
            'temp' => 0.0,
            'widget' => [
                'enabled' => true,
                'position' => setting('chatbase_widget_position', 'bottom-right'),
                'theme' => setting('chatbase_widget_theme', 'light'),
            ],
        ];

        $validated['settings'] = $settings;

        return $validated;
    }
}
