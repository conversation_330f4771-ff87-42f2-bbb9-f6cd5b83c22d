<?php

namespace Botble\Chatbase\Notifications;

use Bo<PERSON><PERSON>\Chatbase\Models\ChatbaseAgent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewLeadNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected array $leadData;
    protected ChatbaseAgent $agent;

    public function __construct(array $leadData, ChatbaseAgent $agent)
    {
        $this->leadData = $leadData;
        $this->agent = $agent;
    }

    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable): MailMessage
    {
        $leadName = $this->leadData['name'] ?? 'Anonymous';
        $leadEmail = $this->leadData['email'] ?? 'Not provided';
        $leadPhone = $this->leadData['phone'] ?? 'Not provided';

        return (new MailMessage)
            ->subject('New Lead from Your Chatbase Agent')
            ->greeting('Hello!')
            ->line('You have received a new lead from your Chatbase agent.')
            ->line('**Agent:** ' . $this->agent->name)
            ->line('**Store:** ' . $this->agent->store->name)
            ->line('**Lead Details:**')
            ->line('- Name: ' . $leadName)
            ->line('- Email: ' . $leadEmail)
            ->line('- Phone: ' . $leadPhone)
            ->action('View Agent Dashboard', route('marketplace.vendor.chatbase.analytics', $this->agent->id))
            ->line('Thank you for using our Chatbase integration!');
    }

    public function toArray($notifiable): array
    {
        return [
            'type' => 'chatbase_new_lead',
            'agent_id' => $this->agent->id,
            'agent_name' => $this->agent->name,
            'store_name' => $this->agent->store->name,
            'lead_data' => $this->leadData,
            'message' => 'New lead captured by ' . $this->agent->name,
        ];
    }
}
