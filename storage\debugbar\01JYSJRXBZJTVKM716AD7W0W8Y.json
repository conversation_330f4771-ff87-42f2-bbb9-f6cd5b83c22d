{"__meta": {"id": "01JYSJRXBZJTVKM716AD7W0W8Y", "datetime": "2025-06-27 20:55:24", "utime": **********.800515, "method": "GET", "uri": "/vendor/chatbase/1/analytics", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.027323, "end": **********.800528, "duration": 3.773205041885376, "duration_str": "3.77s", "measures": [{"label": "Booting", "start": **********.027323, "relative_start": 0, "end": **********.882932, "relative_end": **********.882932, "duration": 0.****************, "duration_str": "856ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.882944, "relative_start": 0.****************, "end": **********.800529, "relative_end": 9.5367431640625e-07, "duration": 2.****************, "duration_str": "2.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.903399, "relative_start": 0.****************, "end": **********.9161, "relative_end": **********.9161, "duration": 0.012701034545898438, "duration_str": "12.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.518684, "relative_start": 3.***************, "end": **********.798003, "relative_end": **********.798003, "duration": 0.*****************, "duration_str": "279ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.519592, "relative_start": 3.****************, "end": **********.519592, "relative_end": **********.519592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.master", "start": **********.708601, "relative_start": 3.6812779903411865, "end": **********.708601, "relative_end": **********.708601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.header", "start": **********.722727, "relative_start": 3.695404052734375, "end": **********.722727, "relative_end": **********.722727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "start": **********.726811, "relative_start": 3.6994879245758057, "end": **********.726811, "relative_end": **********.726811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.730873, "relative_start": 3.703550100326538, "end": **********.730873, "relative_end": **********.730873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.body", "start": **********.73511, "relative_start": 3.707787036895752, "end": **********.73511, "relative_end": **********.73511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.739554, "relative_start": 3.712230920791626, "end": **********.739554, "relative_end": **********.739554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cac860711b64d1ed1b307f6f90fcca70", "start": **********.742633, "relative_start": 3.7153100967407227, "end": **********.742633, "relative_end": **********.742633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::123a2f76ca745c01f392c79275e8e77b", "start": **********.743597, "relative_start": 3.7162740230560303, "end": **********.743597, "relative_end": **********.743597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.menu", "start": **********.744075, "relative_start": 3.716752052307129, "end": **********.744075, "relative_end": **********.744075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.748386, "relative_start": 3.7210628986358643, "end": **********.748386, "relative_end": **********.748386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.749169, "relative_start": 3.721846103668213, "end": **********.749169, "relative_end": **********.749169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.749498, "relative_start": 3.722174882888794, "end": **********.749498, "relative_end": **********.749498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.750204, "relative_start": 3.7228810787200928, "end": **********.750204, "relative_end": **********.750204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::44c7b6398de487f4c9a7b75a63df0620", "start": **********.751035, "relative_start": 3.7237119674682617, "end": **********.751035, "relative_end": **********.751035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b080be2f3318cacae747aecafa7c7b81", "start": **********.751831, "relative_start": 3.724508047103882, "end": **********.751831, "relative_end": **********.751831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.752727, "relative_start": 3.7254040241241455, "end": **********.752727, "relative_end": **********.752727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d5c0aa2935e8876757cc96faafa137", "start": **********.75351, "relative_start": 3.726186990737915, "end": **********.75351, "relative_end": **********.75351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.754276, "relative_start": 3.7269530296325684, "end": **********.754276, "relative_end": **********.754276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52343c81df17fcb3d5413f6a176637e5", "start": **********.763356, "relative_start": 3.7360329627990723, "end": **********.763356, "relative_end": **********.763356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b2f9278d1a46873aab6d9fa25c439d0b", "start": **********.76439, "relative_start": 3.7370669841766357, "end": **********.76439, "relative_end": **********.76439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.765229, "relative_start": 3.737905979156494, "end": **********.765229, "relative_end": **********.765229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2279ce135fc9e268c1dcef75eaca22a5", "start": **********.772675, "relative_start": 3.745352029800415, "end": **********.772675, "relative_end": **********.772675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cac860711b64d1ed1b307f6f90fcca70", "start": **********.773234, "relative_start": 3.745910882949829, "end": **********.773234, "relative_end": **********.773234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.menu", "start": **********.781937, "relative_start": 3.7546138763427734, "end": **********.781937, "relative_end": **********.781937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.784917, "relative_start": 3.757594108581543, "end": **********.784917, "relative_end": **********.784917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.78524, "relative_start": 3.7579169273376465, "end": **********.78524, "relative_end": **********.78524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.785521, "relative_start": 3.7581980228424072, "end": **********.785521, "relative_end": **********.785521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.785782, "relative_start": 3.7584590911865234, "end": **********.785782, "relative_end": **********.785782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::44c7b6398de487f4c9a7b75a63df0620", "start": **********.786052, "relative_start": 3.7587289810180664, "end": **********.786052, "relative_end": **********.786052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b080be2f3318cacae747aecafa7c7b81", "start": **********.786313, "relative_start": 3.7589900493621826, "end": **********.786313, "relative_end": **********.786313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.786573, "relative_start": 3.7592499256134033, "end": **********.786573, "relative_end": **********.786573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d5c0aa2935e8876757cc96faafa137", "start": **********.786841, "relative_start": 3.7595179080963135, "end": **********.786841, "relative_end": **********.786841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.78712, "relative_start": 3.7597970962524414, "end": **********.78712, "relative_end": **********.78712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52343c81df17fcb3d5413f6a176637e5", "start": **********.7874, "relative_start": 3.7600769996643066, "end": **********.7874, "relative_end": **********.7874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b2f9278d1a46873aab6d9fa25c439d0b", "start": **********.787762, "relative_start": 3.760438919067383, "end": **********.787762, "relative_end": **********.787762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.788176, "relative_start": 3.7608530521392822, "end": **********.788176, "relative_end": **********.788176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "start": **********.791398, "relative_start": 3.7640750408172607, "end": **********.791398, "relative_end": **********.791398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b89294322e8e8bb5796243859a707d6a", "start": **********.792955, "relative_start": 3.765631914138794, "end": **********.792955, "relative_end": **********.792955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.794617, "relative_start": 3.767293930053711, "end": **********.794617, "relative_end": **********.794617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.footer", "start": **********.795334, "relative_start": 3.7680110931396484, "end": **********.795334, "relative_end": **********.795334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 53366704, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 41, "nb_templates": 41, "templates": [{"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.519557, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.master", "param_count": null, "params": [], "start": **********.708575, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/master.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.header", "param_count": null, "params": [], "start": **********.722701, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/header.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "param_count": null, "params": [], "start": **********.726785, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/header-meta.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}}, {"name": "assets::header", "param_count": null, "params": [], "start": **********.73085, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.body", "param_count": null, "params": [], "start": **********.735085, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.body", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fbody.blade.php&line=1", "ajax": false, "filename": "body.blade.php", "line": "?"}}, {"name": "__components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.739532, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}}, {"name": "__components::cac860711b64d1ed1b307f6f90fcca70", "param_count": null, "params": [], "start": **********.742592, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cac860711b64d1ed1b307f6f90fcca70.blade.php__components::cac860711b64d1ed1b307f6f90fcca70", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcac860711b64d1ed1b307f6f90fcca70.blade.php&line=1", "ajax": false, "filename": "cac860711b64d1ed1b307f6f90fcca70.blade.php", "line": "?"}}, {"name": "__components::123a2f76ca745c01f392c79275e8e77b", "param_count": null, "params": [], "start": **********.743575, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/123a2f76ca745c01f392c79275e8e77b.blade.php__components::123a2f76ca745c01f392c79275e8e77b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F123a2f76ca745c01f392c79275e8e77b.blade.php&line=1", "ajax": false, "filename": "123a2f76ca745c01f392c79275e8e77b.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.menu", "param_count": null, "params": [], "start": **********.744053, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}}, {"name": "__components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.748364, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}}, {"name": "__components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.749148, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}}, {"name": "__components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.749478, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}}, {"name": "__components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.750183, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}}, {"name": "__components::44c7b6398de487f4c9a7b75a63df0620", "param_count": null, "params": [], "start": **********.751013, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/44c7b6398de487f4c9a7b75a63df0620.blade.php__components::44c7b6398de487f4c9a7b75a63df0620", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F44c7b6398de487f4c9a7b75a63df0620.blade.php&line=1", "ajax": false, "filename": "44c7b6398de487f4c9a7b75a63df0620.blade.php", "line": "?"}}, {"name": "__components::b080be2f3318cacae747aecafa7c7b81", "param_count": null, "params": [], "start": **********.751809, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b080be2f3318cacae747aecafa7c7b81.blade.php__components::b080be2f3318cacae747aecafa7c7b81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb080be2f3318cacae747aecafa7c7b81.blade.php&line=1", "ajax": false, "filename": "b080be2f3318cacae747aecafa7c7b81.blade.php", "line": "?"}}, {"name": "__components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.752704, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}}, {"name": "__components::c5d5c0aa2935e8876757cc96faafa137", "param_count": null, "params": [], "start": **********.753487, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c5d5c0aa2935e8876757cc96faafa137.blade.php__components::c5d5c0aa2935e8876757cc96faafa137", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc5d5c0aa2935e8876757cc96faafa137.blade.php&line=1", "ajax": false, "filename": "c5d5c0aa2935e8876757cc96faafa137.blade.php", "line": "?"}}, {"name": "__components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.754255, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}}, {"name": "__components::52343c81df17fcb3d5413f6a176637e5", "param_count": null, "params": [], "start": **********.763324, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52343c81df17fcb3d5413f6a176637e5.blade.php__components::52343c81df17fcb3d5413f6a176637e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52343c81df17fcb3d5413f6a176637e5.blade.php&line=1", "ajax": false, "filename": "52343c81df17fcb3d5413f6a176637e5.blade.php", "line": "?"}}, {"name": "__components::b2f9278d1a46873aab6d9fa25c439d0b", "param_count": null, "params": [], "start": **********.764369, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b2f9278d1a46873aab6d9fa25c439d0b.blade.php__components::b2f9278d1a46873aab6d9fa25c439d0b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb2f9278d1a46873aab6d9fa25c439d0b.blade.php&line=1", "ajax": false, "filename": "b2f9278d1a46873aab6d9fa25c439d0b.blade.php", "line": "?"}}, {"name": "__components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.765208, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}}, {"name": "__components::2279ce135fc9e268c1dcef75eaca22a5", "param_count": null, "params": [], "start": **********.772631, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2279ce135fc9e268c1dcef75eaca22a5.blade.php__components::2279ce135fc9e268c1dcef75eaca22a5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2279ce135fc9e268c1dcef75eaca22a5.blade.php&line=1", "ajax": false, "filename": "2279ce135fc9e268c1dcef75eaca22a5.blade.php", "line": "?"}}, {"name": "__components::cac860711b64d1ed1b307f6f90fcca70", "param_count": null, "params": [], "start": **********.773213, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cac860711b64d1ed1b307f6f90fcca70.blade.php__components::cac860711b64d1ed1b307f6f90fcca70", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcac860711b64d1ed1b307f6f90fcca70.blade.php&line=1", "ajax": false, "filename": "cac860711b64d1ed1b307f6f90fcca70.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.menu", "param_count": null, "params": [], "start": **********.781914, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}}, {"name": "__components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.784897, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}}, {"name": "__components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.78522, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}}, {"name": "__components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.785499, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}}, {"name": "__components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.78576, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}}, {"name": "__components::44c7b6398de487f4c9a7b75a63df0620", "param_count": null, "params": [], "start": **********.78603, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/44c7b6398de487f4c9a7b75a63df0620.blade.php__components::44c7b6398de487f4c9a7b75a63df0620", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F44c7b6398de487f4c9a7b75a63df0620.blade.php&line=1", "ajax": false, "filename": "44c7b6398de487f4c9a7b75a63df0620.blade.php", "line": "?"}}, {"name": "__components::b080be2f3318cacae747aecafa7c7b81", "param_count": null, "params": [], "start": **********.786291, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b080be2f3318cacae747aecafa7c7b81.blade.php__components::b080be2f3318cacae747aecafa7c7b81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb080be2f3318cacae747aecafa7c7b81.blade.php&line=1", "ajax": false, "filename": "b080be2f3318cacae747aecafa7c7b81.blade.php", "line": "?"}}, {"name": "__components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.786551, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}}, {"name": "__components::c5d5c0aa2935e8876757cc96faafa137", "param_count": null, "params": [], "start": **********.786811, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c5d5c0aa2935e8876757cc96faafa137.blade.php__components::c5d5c0aa2935e8876757cc96faafa137", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc5d5c0aa2935e8876757cc96faafa137.blade.php&line=1", "ajax": false, "filename": "c5d5c0aa2935e8876757cc96faafa137.blade.php", "line": "?"}}, {"name": "__components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.787101, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}}, {"name": "__components::52343c81df17fcb3d5413f6a176637e5", "param_count": null, "params": [], "start": **********.787379, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52343c81df17fcb3d5413f6a176637e5.blade.php__components::52343c81df17fcb3d5413f6a176637e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52343c81df17fcb3d5413f6a176637e5.blade.php&line=1", "ajax": false, "filename": "52343c81df17fcb3d5413f6a176637e5.blade.php", "line": "?"}}, {"name": "__components::b2f9278d1a46873aab6d9fa25c439d0b", "param_count": null, "params": [], "start": **********.787725, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b2f9278d1a46873aab6d9fa25c439d0b.blade.php__components::b2f9278d1a46873aab6d9fa25c439d0b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb2f9278d1a46873aab6d9fa25c439d0b.blade.php&line=1", "ajax": false, "filename": "b2f9278d1a46873aab6d9fa25c439d0b.blade.php", "line": "?"}}, {"name": "__components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.788155, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "param_count": null, "params": [], "start": **********.791369, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/partials/language-switcher.blade.phpplugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}}, {"name": "__components::b89294322e8e8bb5796243859a707d6a", "param_count": null, "params": [], "start": **********.792922, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b89294322e8e8bb5796243859a707d6a.blade.php__components::b89294322e8e8bb5796243859a707d6a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb89294322e8e8bb5796243859a707d6a.blade.php&line=1", "ajax": false, "filename": "b89294322e8e8bb5796243859a707d6a.blade.php", "line": "?"}}, {"name": "assets::footer", "param_count": null, "params": [], "start": **********.794596, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "plugins/marketplace::themes.vendor-dashboard.layouts.footer", "param_count": null, "params": [], "start": **********.795306, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/footer.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00364, "accumulated_duration_str": "3.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.932191, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 11.538}, {"sql": "select * from `ec_customers` where `id` = 410 limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "vendor", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Middleware\\RedirectIfNotVendor.php", "line": 14}], "start": **********.9362612, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.538, "width_percent": 12.088}, {"sql": "select * from `mp_stores` where `mp_stores`.`customer_id` = 410 and `mp_stores`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 142}, {"index": 24, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\AnalyticsController.php", "line": 18}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9448621, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 23.626, "width_percent": 13.736}, {"sql": "select * from `chatbase_agents` where `id` = '1' and `store_id` = 410 limit 1", "type": "query", "params": [], "bindings": ["1", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\AnalyticsController.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.947704, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 37.363, "width_percent": 9.615}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.704175, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 46.978, "width_percent": 14.286}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 410 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "plugins/marketplace::themes.vendor-dashboard.layouts.body", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.php", "line": 117}], "start": **********.7678208, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 61.264, "width_percent": 16.758}, {"sql": "select * from `mp_vendor_info` where `mp_vendor_info`.`customer_id` = 410 and `mp_vendor_info`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 142}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Providers\\MarketplaceServiceProvider.php", "line": 540}, {"index": 32, "namespace": "view", "name": "plugins/marketplace::themes.vendor-dashboard.layouts.body", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.php", "line": 170}], "start": **********.774374, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 78.022, "width_percent": 11.813}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.779185, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 89.835, "width_percent": 10.165}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Chatbase\\Models\\ChatbaseAgent": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=1", "ajax": false, "filename": "ChatbaseAgent.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Marketplace\\Models\\VendorInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FVendorInfo.php&line=1", "ajax": false, "filename": "VendorInfo.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/vendor/chatbase/1/analytics", "action_name": "marketplace.vendor.chatbase.analytics", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index", "uri": "GET vendor/chatbase/{id}/analytics", "controller": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FAnalyticsController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Chatbase\\Http\\Controllers\\Fronts", "prefix": "vendor/chatbase", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FAnalyticsController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php:13-49</a>", "middleware": "web, core, vendor", "duration": "3.77s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1030871859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1030871859\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1198534674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1198534674\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1607946356 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://muhrak.gc/vendor/chatbase</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3195 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlDTjEzRXB6SlpmaEpNdU05a0dYclE9PSIsInZhbHVlIjoiQUJTdmJNQ21CbDQ1Ump2ZmsxUDVsZHJwVk1IZVNWeVBHRDVGSHVud3Zudy9xZ0J1dFZVeTFUZGlKNExuUmwrNDVDaFU3V1pZTnpXNTJFZm1TZVExeGFkWEE5eFh2ZlBjUzdHNnUydm9tTEpRaHVWS21JaWRPbEoyUlpZdGhLRjNBRUl6b1NuWWJ6cjVROTN0TUZCZ09sa2lRNlM5c2ZnYUVvUXUrTG9YOTc2UFFPMW1McmRVN09ELzVMQytXN3pMUGJjSURJbncxVjRKNkNXbUtpWW81VUhiTnNmVk5kLy92bmZJUm9nbzNCMD0iLCJtYWMiOiIwNDExNTA2N2Y2MDJiZTc2NjMxYjMwNjE1YjllMjBkMDJjMmU0M2Q2OTc1NWQ3Y2Y5YjBhOWEzMjE5ZWJiYmYxIiwidGFnIjoiIn0%3D; chatbase_anon_id=46b86d34-f4ab-44b2-98e8-d732019a3e3d; XSRF-TOKEN=eyJpdiI6IkRwQVFWRmJWU1czU0k1NXgycDVUUFE9PSIsInZhbHVlIjoiakk2TitEQ2lSNjdtQmRIYmoyUitTeE5mV2UwNnEvTEllR0NOblJRaEthbjU4R2lvenp4VnZ2RTJQT21EdkZlVlpQZER1VWVPTjVoL1JTY1gyblhaK1dZY3JoMzMxaUg1ZUxiTnUxMHJ6ckZTWjcrbldVaFN5Y3RMbkQ2NTR2V1IiLCJtYWMiOiJhNDY5YTBmODM1OTUyZTNlZDhlNDU4ODI5ZGM5ZGRjNDRlODhkOTBkMjkwMzcxNDIyNTNkNjA4YTA0NzU3Y2UwIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Im50aTRvTlVidStXek5Qam51d2NRVGc9PSIsInZhbHVlIjoiUFp2SFdtUlYwQWZlcGlBSEJab2YrWFcrczBhSDRCd1ZSK0I1VWladmVVN0pHSk53RDRJWnlVaEsxc1RWdDdoZXNVb0tlbXlwTkl2eDVUdzdFaEthRitzcWhGN2tJNmNmVDUyNGM1NEpKa3FmZDFVc2dnZGFCenl1cHJwYXVDelMiLCJtYWMiOiJkNTZjYWM0YTQ2ODI2YWQyMmQ4ODBmYWQyOGI5NmVkMDJmZjA2ZTJhM2UwY2MwMTkwYzlkMjdkNGViYzcwNDY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607946356\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-484598396 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"125 characters\">410|re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc|$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n  \"<span class=sf-dump-key>chatbase_anon_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yrLyALnx9eBThTSh0iG06HRtjQ1Ex23uOlcwUDZh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484598396\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 20:55:24 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1473657834 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://muhrak.gc/vendor/chatbase/1/analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>410</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>10821</span> => <span class=sf-dump-num>1751051544</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473657834\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/vendor/chatbase/1/analytics", "action_name": "marketplace.vendor.chatbase.analytics", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index"}, "badge": null}}