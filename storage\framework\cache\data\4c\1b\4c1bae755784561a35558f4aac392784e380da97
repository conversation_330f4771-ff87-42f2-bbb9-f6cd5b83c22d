1751148464O:42:"Illuminate\Pagination\LengthAwarePaginator":11:{s:8:" * items";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:10:{i:0;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:42;s:4:"name";s:8:"LeeRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"88425343284";s:10:"created_at";s:19:"2025-05-13 16:01:15";}s:11:" * original";a:5:{s:2:"id";i:42;s:4:"name";s:8:"LeeRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"88425343284";s:10:"created_at";s:19:"2025-05-13 16:01:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:41;s:4:"name";s:12:"Andre Frantz";s:5:"email";s:27:"<EMAIL>";s:5:"phone";s:9:"497546130";s:10:"created_at";s:19:"2025-05-11 09:34:41";}s:11:" * original";a:5:{s:2:"id";i:41;s:4:"name";s:12:"Andre Frantz";s:5:"email";s:27:"<EMAIL>";s:5:"phone";s:9:"497546130";s:10:"created_at";s:19:"2025-05-11 09:34:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:40;s:4:"name";s:8:"TedRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"82615548276";s:10:"created_at";s:19:"2025-05-08 21:51:03";}s:11:" * original";a:5:{s:2:"id";i:40;s:4:"name";s:8:"TedRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"82615548276";s:10:"created_at";s:19:"2025-05-08 21:51:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:39;s:4:"name";s:10:"DjohnRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"88423782569";s:10:"created_at";s:19:"2025-05-07 20:10:02";}s:11:" * original";a:5:{s:2:"id";i:39;s:4:"name";s:10:"DjohnRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"88423782569";s:10:"created_at";s:19:"2025-05-07 20:10:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:38;s:4:"name";s:15:"Violette Haynie";s:5:"email";s:25:"<EMAIL>";s:5:"phone";s:9:"493771665";s:10:"created_at";s:19:"2025-05-05 17:54:21";}s:11:" * original";a:5:{s:2:"id";i:38;s:4:"name";s:15:"Violette Haynie";s:5:"email";s:25:"<EMAIL>";s:5:"phone";s:9:"493771665";s:10:"created_at";s:19:"2025-05-05 17:54:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:5;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:37;s:4:"name";s:9:"JohnRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"88248939526";s:10:"created_at";s:19:"2025-05-01 16:21:05";}s:11:" * original";a:5:{s:2:"id";i:37;s:4:"name";s:9:"JohnRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"88248939526";s:10:"created_at";s:19:"2025-05-01 16:21:05";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:6;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:36;s:4:"name";s:10:"DavidKnina";s:5:"email";s:21:"<EMAIL>";s:5:"phone";s:11:"82187953597";s:10:"created_at";s:19:"2025-05-01 07:34:52";}s:11:" * original";a:5:{s:2:"id";i:36;s:4:"name";s:10:"DavidKnina";s:5:"email";s:21:"<EMAIL>";s:5:"phone";s:11:"82187953597";s:10:"created_at";s:19:"2025-05-01 07:34:52";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:7;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:35;s:4:"name";s:10:"FreyaRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"89259725574";s:10:"created_at";s:19:"2025-05-01 01:33:10";}s:11:" * original";a:5:{s:2:"id";i:35;s:4:"name";s:10:"FreyaRoult";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:11:"89259725574";s:10:"created_at";s:19:"2025-05-01 01:33:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:8;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:34;s:4:"name";s:8:"TedRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"82789491174";s:10:"created_at";s:19:"2025-04-30 12:19:57";}s:11:" * original";a:5:{s:2:"id";i:34;s:4:"name";s:8:"TedRoult";s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"82789491174";s:10:"created_at";s:19:"2025-04-30 12:19:57";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:9;O:29:"Botble\Contact\Models\Contact":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:33;s:4:"name";s:11:"GeorgeRoult";s:5:"email";s:21:"<EMAIL>";s:5:"phone";s:11:"88548175468";s:10:"created_at";s:19:"2025-04-28 12:58:24";}s:11:" * original";a:5:{s:2:"id";i:33;s:4:"name";s:11:"GeorgeRoult";s:5:"email";s:21:"<EMAIL>";s:5:"phone";s:11:"88548175468";s:10:"created_at";s:19:"2025-04-28 12:58:24";}s:10:" * changes";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:10:" * perPage";i:10;s:14:" * currentPage";i:1;s:7:" * path";s:23:"https://muhrak.gc/admin";s:8:" * query";a:0:{}s:11:" * fragment";N;s:11:" * pageName";s:4:"page";s:10:"onEachSide";i:3;s:10:" * options";a:2:{s:4:"path";s:23:"https://muhrak.gc/admin";s:8:"pageName";s:4:"page";}s:8:" * total";i:34;s:11:" * lastPage";i:4;}