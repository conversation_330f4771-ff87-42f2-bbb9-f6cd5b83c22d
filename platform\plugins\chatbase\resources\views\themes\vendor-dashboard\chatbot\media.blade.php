@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ __('Chatbot Media Management') }}</h4>
                    <div class="card-header-action">
                        <a href="{{ route('marketplace.vendor.chatbot.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {{ __('Back to Chatbot') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(!$agent->chatbot_id)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ __('Chatbot must be created before you can manage media. Please wait for the chatbot creation to complete.') }}
                        </div>
                    @else
                        <div class="row">
                            <!-- Chatbot Icon -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">{{ __('Chatbot Icon') }}</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        @if($agent->icon_url)
                                            <div class="mb-3">
                                                <img src="{{ $agent->icon_url }}" alt="Chatbot Icon" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                            </div>
                                            <form action="{{ route('marketplace.vendor.chatbot.media.icon.delete', $agent->id) }}" method="POST" class="d-inline"
                                                  onsubmit="return confirm('{{ __('Are you sure you want to delete the chatbot icon?') }}')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i> {{ __('Delete Icon') }}
                                                </button>
                                            </form>
                                        @else
                                            <div class="mb-3">
                                                <i class="fas fa-robot fa-5x text-muted"></i>
                                                <p class="text-muted mt-2">{{ __('No icon uploaded') }}</p>
                                            </div>
                                        @endif
                                        
                                        <hr>
                                        
                                        <form action="{{ route('marketplace.vendor.chatbot.media.icon.upload', $agent->id) }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <div class="mb-3">
                                                <label for="icon" class="form-label">{{ __('Upload New Icon') }}</label>
                                                <input type="file" class="form-control" id="icon" name="icon" accept="image/*" required>
                                                <small class="text-muted">{{ __('Supported formats: PNG, JPG, JPEG, GIF. Max size: 2MB') }}</small>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-upload"></i> {{ __('Upload Icon') }}
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Picture -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">{{ __('Profile Picture') }}</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        @if($agent->profile_picture_url)
                                            <div class="mb-3">
                                                <img src="{{ $agent->profile_picture_url }}" alt="Profile Picture" class="img-thumbnail rounded-circle" style="max-width: 150px; max-height: 150px;">
                                            </div>
                                            <form action="{{ route('marketplace.vendor.chatbot.media.profile-picture.delete', $agent->id) }}" method="POST" class="d-inline"
                                                  onsubmit="return confirm('{{ __('Are you sure you want to delete the profile picture?') }}')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i> {{ __('Delete Picture') }}
                                                </button>
                                            </form>
                                        @else
                                            <div class="mb-3">
                                                <i class="fas fa-user-circle fa-5x text-muted"></i>
                                                <p class="text-muted mt-2">{{ __('No profile picture uploaded') }}</p>
                                            </div>
                                        @endif
                                        
                                        <hr>
                                        
                                        <form action="{{ route('marketplace.vendor.chatbot.media.profile-picture.upload', $agent->id) }}" method="POST" enctype="multipart/form-data">
                                            @csrf
                                            <div class="mb-3">
                                                <label for="profile_picture" class="form-label">{{ __('Upload New Profile Picture') }}</label>
                                                <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*" required>
                                                <small class="text-muted">{{ __('Supported formats: PNG, JPG, JPEG, GIF. Max size: 2MB') }}</small>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-upload"></i> {{ __('Upload Picture') }}
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> {{ __('Media Guidelines') }}</h6>
                                    <ul class="mb-0">
                                        <li>{{ __('Icon: Used as the chatbot widget icon on your website') }}</li>
                                        <li>{{ __('Profile Picture: Displayed in chat conversations as the chatbot avatar') }}</li>
                                        <li>{{ __('Recommended size: 256x256 pixels for best quality') }}</li>
                                        <li>{{ __('Use clear, recognizable images that represent your brand') }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
