{"__meta": {"id": "01JYSQMM449SKER6NW8NBQECFZ", "datetime": "2025-06-27 22:20:27", "utime": **********.140847, "method": "GET", "uri": "/vendor/chatbase/1/settings", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.319363, "end": **********.140859, "duration": 0.821495771408081, "duration_str": "821ms", "measures": [{"label": "Booting", "start": **********.319363, "relative_start": 0, "end": **********.938504, "relative_end": **********.938504, "duration": 0.****************, "duration_str": "619ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.938514, "relative_start": 0.****************, "end": **********.140861, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.956705, "relative_start": 0.****************, "end": **********.961668, "relative_end": **********.961668, "duration": 0.004962921142578125, "duration_str": "4.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.forms.base", "start": **********.006765, "relative_start": 0.****************, "end": **********.006765, "relative_end": **********.006765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form", "start": **********.007461, "relative_start": 0.****************, "end": **********.007461, "relative_end": **********.007461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.011046, "relative_start": 0.6916828155517578, "end": **********.011046, "relative_end": **********.011046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.01172, "relative_start": 0.6923568248748779, "end": **********.01172, "relative_end": **********.01172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.012567, "relative_start": 0.6932039260864258, "end": **********.012567, "relative_end": **********.012567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.013819, "relative_start": 0.6944558620452881, "end": **********.013819, "relative_end": **********.013819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.014459, "relative_start": 0.6950957775115967, "end": **********.014459, "relative_end": **********.014459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.014835, "relative_start": 0.6954717636108398, "end": **********.014835, "relative_end": **********.014835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.015287, "relative_start": 0.6959238052368164, "end": **********.015287, "relative_end": **********.015287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.015672, "relative_start": 0.6963088512420654, "end": **********.015672, "relative_end": **********.015672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.016017, "relative_start": 0.6966538429260254, "end": **********.016017, "relative_end": **********.016017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.016417, "relative_start": 0.6970539093017578, "end": **********.016417, "relative_end": **********.016417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.017026, "relative_start": 0.6976628303527832, "end": **********.017026, "relative_end": **********.017026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.017465, "relative_start": 0.6981019973754883, "end": **********.017465, "relative_end": **********.017465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.017689, "relative_start": 0.6983258724212646, "end": **********.017689, "relative_end": **********.017689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.017939, "relative_start": 0.6985759735107422, "end": **********.017939, "relative_end": **********.017939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.018285, "relative_start": 0.6989219188690186, "end": **********.018285, "relative_end": **********.018285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.018688, "relative_start": 0.6993248462677002, "end": **********.018688, "relative_end": **********.018688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.019102, "relative_start": 0.6997389793395996, "end": **********.019102, "relative_end": **********.019102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.019454, "relative_start": 0.7000908851623535, "end": **********.019454, "relative_end": **********.019454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.01986, "relative_start": 0.7004969120025635, "end": **********.01986, "relative_end": **********.01986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.02007, "relative_start": 0.700706958770752, "end": **********.02007, "relative_end": **********.02007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.020309, "relative_start": 0.7009458541870117, "end": **********.020309, "relative_end": **********.020309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.020719, "relative_start": 0.7013559341430664, "end": **********.020719, "relative_end": **********.020719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.02114, "relative_start": 0.7017769813537598, "end": **********.02114, "relative_end": **********.02114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.label", "start": **********.021551, "relative_start": 0.7021877765655518, "end": **********.021551, "relative_end": **********.021551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.021988, "relative_start": 0.7026247978210449, "end": **********.021988, "relative_end": **********.021988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.02235, "relative_start": 0.7029869556427002, "end": **********.02235, "relative_end": **********.02235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.022771, "relative_start": 0.7034077644348145, "end": **********.022771, "relative_end": **********.022771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.023001, "relative_start": 0.7036378383636475, "end": **********.023001, "relative_end": **********.023001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.023253, "relative_start": 0.7038898468017578, "end": **********.023253, "relative_end": **********.023253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.body.index", "start": **********.023641, "relative_start": 0.7042779922485352, "end": **********.023641, "relative_end": **********.023641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.index", "start": **********.023982, "relative_start": 0.7046189308166504, "end": **********.023982, "relative_end": **********.023982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.027034, "relative_start": 0.7076709270477295, "end": **********.027034, "relative_end": **********.027034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": **********.027412, "relative_start": 0.7080488204956055, "end": **********.027412, "relative_end": **********.027412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.title", "start": **********.028854, "relative_start": 0.7094907760620117, "end": **********.028854, "relative_end": **********.028854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.header.index", "start": **********.029282, "relative_start": 0.7099189758300781, "end": **********.029282, "relative_end": **********.029282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.029767, "relative_start": 0.7104039192199707, "end": **********.029767, "relative_end": **********.029767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.03049, "relative_start": 0.7111268043518066, "end": **********.03049, "relative_end": **********.03049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.033375, "relative_start": 0.7140119075775146, "end": **********.033375, "relative_end": **********.033375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.033653, "relative_start": 0.7142899036407471, "end": **********.033653, "relative_end": **********.033653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.034678, "relative_start": 0.7153148651123047, "end": **********.034678, "relative_end": **********.034678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.body.index", "start": **********.035168, "relative_start": 0.7158048152923584, "end": **********.035168, "relative_end": **********.035168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::card.index", "start": **********.035392, "relative_start": 0.7160289287567139, "end": **********.035392, "relative_end": **********.035392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.037516, "relative_start": 0.7181529998779297, "end": **********.037516, "relative_end": **********.037516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.038037, "relative_start": 0.7186739444732666, "end": **********.038037, "relative_end": **********.038037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.038619, "relative_start": 0.7192559242248535, "end": **********.038619, "relative_end": **********.038619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.038878, "relative_start": 0.7195148468017578, "end": **********.038878, "relative_end": **********.038878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.039444, "relative_start": 0.7200808525085449, "end": **********.039444, "relative_end": **********.039444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.04102, "relative_start": 0.7216567993164062, "end": **********.04102, "relative_end": **********.04102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.042212, "relative_start": 0.7228488922119141, "end": **********.042212, "relative_end": **********.042212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.059453, "relative_start": 0.7400898933410645, "end": **********.059453, "relative_end": **********.059453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.master", "start": **********.060265, "relative_start": 0.7409019470214844, "end": **********.060265, "relative_end": **********.060265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.header", "start": **********.075856, "relative_start": 0.7564928531646729, "end": **********.075856, "relative_end": **********.075856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "start": **********.079692, "relative_start": 0.7603287696838379, "end": **********.079692, "relative_end": **********.079692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.083838, "relative_start": 0.7644748687744141, "end": **********.083838, "relative_end": **********.083838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.body", "start": **********.087302, "relative_start": 0.7679388523101807, "end": **********.087302, "relative_end": **********.087302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.08859, "relative_start": 0.7692267894744873, "end": **********.08859, "relative_end": **********.08859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cac860711b64d1ed1b307f6f90fcca70", "start": **********.091432, "relative_start": 0.772068977355957, "end": **********.091432, "relative_end": **********.091432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::123a2f76ca745c01f392c79275e8e77b", "start": **********.092137, "relative_start": 0.7727739810943604, "end": **********.092137, "relative_end": **********.092137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.menu", "start": **********.09258, "relative_start": 0.773216962814331, "end": **********.09258, "relative_end": **********.09258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.097259, "relative_start": 0.7778959274291992, "end": **********.097259, "relative_end": **********.097259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.098096, "relative_start": 0.7787327766418457, "end": **********.098096, "relative_end": **********.098096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.098426, "relative_start": 0.7790629863739014, "end": **********.098426, "relative_end": **********.098426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.099121, "relative_start": 0.7797579765319824, "end": **********.099121, "relative_end": **********.099121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::44c7b6398de487f4c9a7b75a63df0620", "start": **********.0999, "relative_start": 0.7805368900299072, "end": **********.0999, "relative_end": **********.0999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b080be2f3318cacae747aecafa7c7b81", "start": **********.100724, "relative_start": 0.7813608646392822, "end": **********.100724, "relative_end": **********.100724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.10147, "relative_start": 0.782106876373291, "end": **********.10147, "relative_end": **********.10147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d5c0aa2935e8876757cc96faafa137", "start": **********.1022, "relative_start": 0.7828369140625, "end": **********.1022, "relative_end": **********.1022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.102969, "relative_start": 0.7836058139801025, "end": **********.102969, "relative_end": **********.102969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52343c81df17fcb3d5413f6a176637e5", "start": **********.103728, "relative_start": 0.7843649387359619, "end": **********.103728, "relative_end": **********.103728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b2f9278d1a46873aab6d9fa25c439d0b", "start": **********.104462, "relative_start": 0.7850987911224365, "end": **********.104462, "relative_end": **********.104462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.10526, "relative_start": 0.7858967781066895, "end": **********.10526, "relative_end": **********.10526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2279ce135fc9e268c1dcef75eaca22a5", "start": **********.113631, "relative_start": 0.7942678928375244, "end": **********.113631, "relative_end": **********.113631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cac860711b64d1ed1b307f6f90fcca70", "start": **********.114033, "relative_start": 0.7946698665618896, "end": **********.114033, "relative_end": **********.114033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.menu", "start": **********.12331, "relative_start": 0.8039469718933105, "end": **********.12331, "relative_end": **********.12331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.126446, "relative_start": 0.8070828914642334, "end": **********.126446, "relative_end": **********.126446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.126727, "relative_start": 0.8073639869689941, "end": **********.126727, "relative_end": **********.126727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.126995, "relative_start": 0.8076319694519043, "end": **********.126995, "relative_end": **********.126995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.127257, "relative_start": 0.8078939914703369, "end": **********.127257, "relative_end": **********.127257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::44c7b6398de487f4c9a7b75a63df0620", "start": **********.127516, "relative_start": 0.8081529140472412, "end": **********.127516, "relative_end": **********.127516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b080be2f3318cacae747aecafa7c7b81", "start": **********.127775, "relative_start": 0.8084118366241455, "end": **********.127775, "relative_end": **********.127775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.128094, "relative_start": 0.8087308406829834, "end": **********.128094, "relative_end": **********.128094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5d5c0aa2935e8876757cc96faafa137", "start": **********.128409, "relative_start": 0.8090457916259766, "end": **********.128409, "relative_end": **********.128409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.128686, "relative_start": 0.8093228340148926, "end": **********.128686, "relative_end": **********.128686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52343c81df17fcb3d5413f6a176637e5", "start": **********.128993, "relative_start": 0.8096299171447754, "end": **********.128993, "relative_end": **********.128993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b2f9278d1a46873aab6d9fa25c439d0b", "start": **********.129269, "relative_start": 0.8099057674407959, "end": **********.129269, "relative_end": **********.129269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.129546, "relative_start": 0.8101828098297119, "end": **********.129546, "relative_end": **********.129546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "start": **********.132149, "relative_start": 0.8127858638763428, "end": **********.132149, "relative_end": **********.132149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b89294322e8e8bb5796243859a707d6a", "start": **********.133669, "relative_start": 0.8143057823181152, "end": **********.133669, "relative_end": **********.133669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.13437, "relative_start": 0.8150069713592529, "end": **********.13437, "relative_end": **********.13437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::themes.vendor-dashboard.layouts.footer", "start": **********.135064, "relative_start": 0.8157007694244385, "end": **********.135064, "relative_end": **********.135064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.137984, "relative_start": 0.8186209201812744, "end": **********.138674, "relative_end": **********.138674, "duration": 0.0006899833679199219, "duration_str": "690μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 65669624, "peak_usage_str": "63MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 92, "nb_templates": 92, "templates": [{"name": "1x plugins/marketplace::themes.vendor-dashboard.forms.base", "param_count": null, "params": [], "start": **********.006736, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/forms/base.blade.phpplugins/marketplace::themes.vendor-dashboard.forms.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fforms%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.forms.base"}, {"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.007441, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "3x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.011025, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.fields.textarea"}, {"name": "4x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.0117, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.label"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::form.label", "param_count": null, "params": [], "start": **********.012546, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/label.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.label"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::form.field", "param_count": null, "params": [], "start": **********.013797, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/field.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.field"}, {"name": "4x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.014438, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.help-block"}, {"name": "4x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.014814, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.errors"}, {"name": "4x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.015267, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.0207, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-select"}, {"name": "1x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.021968, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-select"}, {"name": "2x a74ad8dfacd4f985eb3977517615ce25::card.body.index", "param_count": null, "params": [], "start": **********.023621, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/body/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.body.index"}, {"name": "2x a74ad8dfacd4f985eb3977517615ce25::card.index", "param_count": null, "params": [], "start": **********.023963, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.index"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.027014, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": **********.027392, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php&line=1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.title", "param_count": null, "params": [], "start": **********.028834, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/title.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.title"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::card.header.index", "param_count": null, "params": [], "start": **********.029262, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/header/index.blade.phpa74ad8dfacd4f985eb3977517615ce25::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::card.header.index"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.029747, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php&line=1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.030471, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::button"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.033354, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x __components::5db12cde11beb11dd9ef0499874ec197", "param_count": null, "params": [], "start": **********.034658, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5db12cde11beb11dd9ef0499874ec197.blade.php__components::5db12cde11beb11dd9ef0499874ec197", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5db12cde11beb11dd9ef0499874ec197.blade.php&line=1", "ajax": false, "filename": "5db12cde11beb11dd9ef0499874ec197.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5db12cde11beb11dd9ef0499874ec197"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.059428, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.layouts.master", "param_count": null, "params": [], "start": **********.060244, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/master.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.master"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.layouts.header", "param_count": null, "params": [], "start": **********.075818, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/header.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.header"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "param_count": null, "params": [], "start": **********.07967, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/header-meta.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.header-meta"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.083815, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.layouts.body", "param_count": null, "params": [], "start": **********.087279, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.body", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fbody.blade.php&line=1", "ajax": false, "filename": "body.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.body"}, {"name": "1x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.08857, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x __components::cac860711b64d1ed1b307f6f90fcca70", "param_count": null, "params": [], "start": **********.091411, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cac860711b64d1ed1b307f6f90fcca70.blade.php__components::cac860711b64d1ed1b307f6f90fcca70", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcac860711b64d1ed1b307f6f90fcca70.blade.php&line=1", "ajax": false, "filename": "cac860711b64d1ed1b307f6f90fcca70.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::cac860711b64d1ed1b307f6f90fcca70"}, {"name": "1x __components::123a2f76ca745c01f392c79275e8e77b", "param_count": null, "params": [], "start": **********.092117, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/123a2f76ca745c01f392c79275e8e77b.blade.php__components::123a2f76ca745c01f392c79275e8e77b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F123a2f76ca745c01f392c79275e8e77b.blade.php&line=1", "ajax": false, "filename": "123a2f76ca745c01f392c79275e8e77b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::123a2f76ca745c01f392c79275e8e77b"}, {"name": "2x plugins/marketplace::themes.vendor-dashboard.layouts.menu", "param_count": null, "params": [], "start": **********.092559, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/menu.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.menu"}, {"name": "4x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.097237, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "2x __components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.098075, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5e1ed162565cf31bd543a8427caaef1e"}, {"name": "2x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.099101, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "2x __components::44c7b6398de487f4c9a7b75a63df0620", "param_count": null, "params": [], "start": **********.099843, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/44c7b6398de487f4c9a7b75a63df0620.blade.php__components::44c7b6398de487f4c9a7b75a63df0620", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F44c7b6398de487f4c9a7b75a63df0620.blade.php&line=1", "ajax": false, "filename": "44c7b6398de487f4c9a7b75a63df0620.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::44c7b6398de487f4c9a7b75a63df0620"}, {"name": "2x __components::b080be2f3318cacae747aecafa7c7b81", "param_count": null, "params": [], "start": **********.100704, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b080be2f3318cacae747aecafa7c7b81.blade.php__components::b080be2f3318cacae747aecafa7c7b81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb080be2f3318cacae747aecafa7c7b81.blade.php&line=1", "ajax": false, "filename": "b080be2f3318cacae747aecafa7c7b81.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b080be2f3318cacae747aecafa7c7b81"}, {"name": "2x __components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.10145, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9413e731e9bb6e320e018067130903e9"}, {"name": "2x __components::c5d5c0aa2935e8876757cc96faafa137", "param_count": null, "params": [], "start": **********.10218, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c5d5c0aa2935e8876757cc96faafa137.blade.php__components::c5d5c0aa2935e8876757cc96faafa137", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc5d5c0aa2935e8876757cc96faafa137.blade.php&line=1", "ajax": false, "filename": "c5d5c0aa2935e8876757cc96faafa137.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::c5d5c0aa2935e8876757cc96faafa137"}, {"name": "2x __components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.102949, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::52309442cd989852b03aa65d96b55790"}, {"name": "2x __components::52343c81df17fcb3d5413f6a176637e5", "param_count": null, "params": [], "start": **********.103708, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52343c81df17fcb3d5413f6a176637e5.blade.php__components::52343c81df17fcb3d5413f6a176637e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52343c81df17fcb3d5413f6a176637e5.blade.php&line=1", "ajax": false, "filename": "52343c81df17fcb3d5413f6a176637e5.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::52343c81df17fcb3d5413f6a176637e5"}, {"name": "2x __components::b2f9278d1a46873aab6d9fa25c439d0b", "param_count": null, "params": [], "start": **********.104442, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b2f9278d1a46873aab6d9fa25c439d0b.blade.php__components::b2f9278d1a46873aab6d9fa25c439d0b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb2f9278d1a46873aab6d9fa25c439d0b.blade.php&line=1", "ajax": false, "filename": "b2f9278d1a46873aab6d9fa25c439d0b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b2f9278d1a46873aab6d9fa25c439d0b"}, {"name": "2x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.10524, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::2279ce135fc9e268c1dcef75eaca22a5", "param_count": null, "params": [], "start": **********.113606, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2279ce135fc9e268c1dcef75eaca22a5.blade.php__components::2279ce135fc9e268c1dcef75eaca22a5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2279ce135fc9e268c1dcef75eaca22a5.blade.php&line=1", "ajax": false, "filename": "2279ce135fc9e268c1dcef75eaca22a5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2279ce135fc9e268c1dcef75eaca22a5"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "param_count": null, "params": [], "start": **********.132129, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/partials/language-switcher.blade.phpplugins/marketplace::themes.vendor-dashboard.partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.partials.language-switcher"}, {"name": "1x __components::b89294322e8e8bb5796243859a707d6a", "param_count": null, "params": [], "start": **********.13365, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b89294322e8e8bb5796243859a707d6a.blade.php__components::b89294322e8e8bb5796243859a707d6a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb89294322e8e8bb5796243859a707d6a.blade.php&line=1", "ajax": false, "filename": "b89294322e8e8bb5796243859a707d6a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b89294322e8e8bb5796243859a707d6a"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.134351, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x plugins/marketplace::themes.vendor-dashboard.layouts.footer", "param_count": null, "params": [], "start": **********.135045, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/footer.blade.phpplugins/marketplace::themes.vendor-dashboard.layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::themes.vendor-dashboard.layouts.footer"}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0029000000000000002, "accumulated_duration_str": "2.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.975931, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 17.586}, {"sql": "select * from `ec_customers` where `id` = 410 limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "vendor", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Middleware\\RedirectIfNotVendor.php", "line": 14}], "start": **********.978858, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.586, "width_percent": 13.793}, {"sql": "select * from `mp_stores` where `mp_stores`.`customer_id` = 410 and `mp_stores`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 142}, {"index": 24, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/ChatbaseController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\ChatbaseController.php", "line": 126}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.987189, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 31.379, "width_percent": 15.517}, {"sql": "select * from `chatbase_agents` where `id` = '1' and `store_id` = 410 limit 1", "type": "query", "params": [], "bindings": ["1", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/ChatbaseController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\ChatbaseController.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.989732, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 46.897, "width_percent": 11.379}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 410 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "plugins/marketplace::themes.vendor-dashboard.layouts.body", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.php", "line": 117}], "start": **********.1082692, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 58.276, "width_percent": 15.517}, {"sql": "select * from `mp_vendor_info` where `mp_vendor_info`.`customer_id` = 410 and `mp_vendor_info`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 142}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Providers\\MarketplaceServiceProvider.php", "line": 540}, {"index": 32, "namespace": "view", "name": "plugins/marketplace::themes.vendor-dashboard.layouts.body", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/themes/vendor-dashboard/layouts/body.blade.php", "line": 170}], "start": **********.1151628, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 73.793, "width_percent": 13.103}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.120399, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 86.897, "width_percent": 13.103}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Chatbase\\Models\\ChatbaseAgent": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=1", "ajax": false, "filename": "ChatbaseAgent.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Marketplace\\Models\\VendorInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FVendorInfo.php&line=1", "ajax": false, "filename": "VendorInfo.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/vendor/chatbase/1/settings", "action_name": "marketplace.vendor.chatbase.settings", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\ChatbaseController@settings", "uri": "GET vendor/chatbase/{id}/settings", "controller": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\ChatbaseController@settings<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FChatbaseController.php&line=121\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Chatbase\\Http\\Controllers\\Fronts", "prefix": "vendor/chatbase", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FChatbaseController.php&line=121\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/chatbase/src/Http/Controllers/Fronts/ChatbaseController.php:121-139</a>", "middleware": "web, core, vendor", "duration": "820ms", "peak_memory": "70MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-988918073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-988918073\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1336295542 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1336295542\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1677249419 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://muhrak.gc/vendor/chatbase</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3195 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlDTjEzRXB6SlpmaEpNdU05a0dYclE9PSIsInZhbHVlIjoiQUJTdmJNQ21CbDQ1Ump2ZmsxUDVsZHJwVk1IZVNWeVBHRDVGSHVud3Zudy9xZ0J1dFZVeTFUZGlKNExuUmwrNDVDaFU3V1pZTnpXNTJFZm1TZVExeGFkWEE5eFh2ZlBjUzdHNnUydm9tTEpRaHVWS21JaWRPbEoyUlpZdGhLRjNBRUl6b1NuWWJ6cjVROTN0TUZCZ09sa2lRNlM5c2ZnYUVvUXUrTG9YOTc2UFFPMW1McmRVN09ELzVMQytXN3pMUGJjSURJbncxVjRKNkNXbUtpWW81VUhiTnNmVk5kLy92bmZJUm9nbzNCMD0iLCJtYWMiOiIwNDExNTA2N2Y2MDJiZTc2NjMxYjMwNjE1YjllMjBkMDJjMmU0M2Q2OTc1NWQ3Y2Y5YjBhOWEzMjE5ZWJiYmYxIiwidGFnIjoiIn0%3D; chatbase_anon_id=46b86d34-f4ab-44b2-98e8-d732019a3e3d; XSRF-TOKEN=eyJpdiI6IlB4Tm1KcHUvODY5THUzSVF5NHBzWUE9PSIsInZhbHVlIjoiWm5ramNaWkV1V1l2SVdYbW5pVDg2TzM5OWNpRnhQMjQzMzRlSkNQOGNDQ25WYTRjemNoTmlKUGVTV1AveFFRNmJEWnJLK2U5Rnl4bjExMzArbVdIZ0ZWdllFMG9TN09IQXRLdHg0emV4cDROTlZxYXk1SFp2VVFVZUdSZHowTFQiLCJtYWMiOiI1NmFmYmQ1NDNlOTg1N2E5YzUxZmVlMDJiY2VkNzI5MGEyYjVkYjljM2M0MGZlNzhmNjMxNzYwNTdiMmNhZTVhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IksrOGNrWlJ0NEdFaUp6TlJjOG9qSkE9PSIsInZhbHVlIjoiRjlXS3YzQlJiemlIZDdZVm10WVpoQnQyd2NiMkRDUGtzajR0cGlNOVRaaTRGK3BsVkdHU2RYa0dDR0t1OHQ2OU0yV2hZN2k4d1JxUGErVzJwK0dCOUMvZ1hnSmdUMkNlVUZ3WmpRdmZLVEVPeXhRZkJMcmUxTXZSR1BNZ3NWSHMiLCJtYWMiOiI0OTIwNWE4MTI1OGM1NTkyMzkxMmMyOTIzYzBhMmJiZDgxYzE5YTVkZjZjMzAxZWJmYjkwODU1OTVlMzEzZDg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677249419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-778015833 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"125 characters\">410|re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc|$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n  \"<span class=sf-dump-key>chatbase_anon_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yrLyALnx9eBThTSh0iG06HRtjQ1Ex23uOlcwUDZh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778015833\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-642778390 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 22:20:27 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642778390\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1178869501 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">https://muhrak.gc/admin/chatbase/admin/chatbots</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>410</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>10821</span> => <span class=sf-dump-num>1751051544</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178869501\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/vendor/chatbase/1/settings", "action_name": "marketplace.vendor.chatbase.settings", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\ChatbaseController@settings"}, "badge": null}}