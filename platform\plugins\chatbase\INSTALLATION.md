# Chatbase Integration Plugin - Installation Guide

## Prerequisites

Before installing the Chatbase integration plugin, ensure you have:

1. **Chatbase.co Account**: Sign up at [chatbase.co](https://www.chatbase.co) and obtain your API key
2. **Active Marketplace**: The marketplace plugin must be installed and configured
3. **Vendor System**: Vendors should be able to create stores and access their dashboards
4. **PHP 8.1+**: Required for the plugin to function properly
5. **<PERSON>vel Queue**: Recommended for asynchronous agent creation

## Installation Steps

### 1. Plugin Installation

The plugin is already placed in the correct directory: `platform/plugins/chatbase/`

### 2. Run Database Migrations

Execute the following command to create the necessary database tables:

```bash
php artisan migrate
```

This will create:
- `chatbase_agents` - Stores agent information and settings
- `chatbase_training_sources` - Manages training content for agents

### 3. Activate the Plugin

1. Go to your admin panel
2. Navigate to **Plugins** section
3. Find "Chatbase Integration" in the list
4. Click **Activate**

### 4. Configure API Settings

1. Go to **Settings > Chatbase** in your admin panel
2. Enter your Chatbase API key (found in your Chatbase dashboard)
3. Configure default settings:
   - Default AI Model (recommended: GPT-4o Mini)
   - Widget Position (bottom-right is most common)
   - Widget Theme (light/dark/auto)
   - Auto-embed Widget (enable for automatic integration)

### 5. Test the Configuration

The plugin will automatically test your API key when you save the settings. If there are any issues, you'll see an error message.

## Vendor Usage

### For Vendors

Once the plugin is installed and configured, vendors can:

1. **Access Chatbase Features**:
   - Go to Vendor Dashboard
   - Click on "Chatbase Agent" in the menu

2. **Create an Agent**:
   - Click "Create Agent"
   - Fill in agent name and description
   - Add training content about their store
   - Configure widget settings
   - Submit to create the agent

3. **Manage Training**:
   - Add text content, URLs, or files for training
   - Update existing training sources
   - Monitor character usage

4. **View Analytics**:
   - Access conversation statistics
   - View captured leads
   - Monitor agent performance

### Widget Integration

The chat widget will automatically appear on store pages when:
- The vendor has created an active agent
- Widget is enabled in agent settings
- Auto-embed is enabled in global settings

## Advanced Configuration

### Queue Configuration (Recommended)

For better performance, configure Laravel queues to handle agent creation asynchronously:

1. Set up a queue driver (Redis, database, etc.)
2. Run queue workers: `php artisan queue:work`
3. Agent creation will happen in the background

### Webhook Configuration (Optional)

To receive real-time notifications from Chatbase:

1. In your Chatbase dashboard, set webhook URL to:
   ```
   https://yourdomain.com/api/chatbase/webhook
   ```

2. Configure webhook events you want to receive

### Scheduled Commands

Add this to your `app/Console/Kernel.php` for automatic synchronization:

```php
protected function schedule(Schedule $schedule)
{
    // Sync agents with Chatbase API every hour
    $schedule->command('chatbase:sync-agents')->hourly();
}
```

## Troubleshooting

### Common Issues

1. **"API key required" error**:
   - Ensure API key is properly configured in Settings > Chatbase
   - Verify the API key is valid in your Chatbase account

2. **Agent creation fails**:
   - Check your Chatbase account limits
   - Verify internet connectivity
   - Check Laravel logs for detailed error messages

3. **Widget not appearing**:
   - Ensure agent status is "Active"
   - Check widget is enabled in agent settings
   - Verify auto-embed is enabled in global settings

4. **Training content not saving**:
   - Check character limits in your Chatbase plan
   - Ensure content is not empty
   - Verify database permissions

### Log Files

Check these log files for debugging:
- `storage/logs/laravel.log` - General application logs
- Look for entries containing "Chatbase" for plugin-specific issues

### Support Commands

Useful commands for troubleshooting:

```bash
# Sync all agents with Chatbase API
php artisan chatbase:sync-agents

# Sync specific agent
php artisan chatbase:sync-agents --agent=123

# Force sync even if recently synced
php artisan chatbase:sync-agents --force

# Clear application cache
php artisan cache:clear

# Run database migrations
php artisan migrate --path=platform/plugins/chatbase/database/migrations
```

## Security Considerations

1. **API Key Protection**: Store API keys securely and never expose them in frontend code
2. **Webhook Security**: Implement signature verification for webhook endpoints
3. **User Permissions**: Ensure only authorized vendors can create/manage agents
4. **Data Privacy**: Review Chatbase's privacy policy and ensure compliance

## Performance Optimization

1. **Use Queues**: Enable queue processing for agent creation
2. **Cache Settings**: Cache frequently accessed settings
3. **Database Indexing**: Ensure proper database indexes are in place
4. **CDN**: Use CDN for Chatbase widget scripts if needed

## Updating the Plugin

When updating the plugin:

1. Backup your database
2. Replace plugin files
3. Run migrations: `php artisan migrate`
4. Clear caches: `php artisan cache:clear`
5. Test functionality

## Support

For technical support:
- Check the plugin documentation
- Review Laravel logs for errors
- Contact the development team with specific error messages and steps to reproduce issues

## License

This plugin is proprietary software. Unauthorized distribution or modification is prohibited.
