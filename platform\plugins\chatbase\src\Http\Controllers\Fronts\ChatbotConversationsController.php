<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Chatbase\Services\ChatbaseApiService;
use Illuminate\Http\Request;

class ChatbotConversationsController extends BaseController
{
    public function index(string $id, Request $request, ChatbaseApiService $apiService)
    {
        $this->pageTitle(__('Conversations'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return redirect()
                ->route('marketplace.vendor.chatbot.index')
                ->with('error', __('Chatbot must be created before viewing conversations'));
        }

        $conversations = [];
        $error = null;

        // Get conversations from API
        $result = $apiService->getConversations($agent->chatbot_id, 50);
        
        if ($result['success']) {
            $conversations = $result['data']['conversations'] ?? [];
        } else {
            $error = $result['error'];
        }

        $data = compact('user', 'store', 'agent', 'conversations', 'error');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => view('plugins/chatbase::themes.vendor-dashboard.chatbot.conversations', $data)->render(),
                ]);
        }

        return view('plugins/chatbase::themes.vendor-dashboard.chatbot.conversations', $data);
    }

    public function leads(string $id, Request $request, ChatbaseApiService $apiService)
    {
        $this->pageTitle(__('Leads'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return redirect()
                ->route('marketplace.vendor.chatbot.index')
                ->with('error', __('Chatbot must be created before viewing leads'));
        }

        $leads = [];
        $error = null;

        // Get leads from API
        $result = $apiService->getLeads($agent->chatbot_id);
        
        if ($result['success']) {
            $leads = $result['data']['leads'] ?? [];
        } else {
            $error = $result['error'];
        }

        $data = compact('user', 'store', 'agent', 'leads', 'error');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => view('plugins/chatbase::themes.vendor-dashboard.chatbot.leads', $data)->render(),
                ]);
        }

        return view('plugins/chatbase::themes.vendor-dashboard.chatbot.leads', $data);
    }

    public function sendMessage(string $id, Request $request, ChatbaseApiService $apiService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot must be created before sending messages'));
        }

        $request->validate([
            'message' => 'required|string|max:1000',
            'conversation_id' => 'nullable|string',
        ]);

        $result = $apiService->messageChatbot(
            $agent->chatbot_id,
            $request->input('message'),
            $request->input('conversation_id')
        );

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setData($result['data'])
            ->setMessage(__('Message sent successfully!'));
    }
}
