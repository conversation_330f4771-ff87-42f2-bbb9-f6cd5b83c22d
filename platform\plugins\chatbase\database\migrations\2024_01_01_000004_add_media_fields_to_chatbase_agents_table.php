<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('chatbase_agents', function (Blueprint $table) {
            $table->string('icon_url')->nullable()->after('temp');
            $table->string('profile_picture_url')->nullable()->after('icon_url');
        });
    }

    public function down(): void
    {
        Schema::table('chatbase_agents', function (Blueprint $table) {
            $table->dropColumn(['icon_url', 'profile_picture_url']);
        });
    }
};
