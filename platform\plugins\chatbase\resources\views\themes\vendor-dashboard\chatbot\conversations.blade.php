@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ __('Conversations') }}</h4>
                    <div class="card-header-action">
                        <a href="{{ route('marketplace.vendor.chatbot.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {{ __('Back to Chatbot') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($error)
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ __('Error loading conversations') }}: {{ $error }}
                        </div>
                    @elseif(empty($conversations))
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5>{{ __('No Conversations Yet') }}</h5>
                            <p class="text-muted">{{ __('Conversations will appear here once customers start chatting with your bot.') }}</p>
                        </div>
                    @else
                        <div class="row">
                            @foreach($conversations as $conversation)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">
                                                {{ __('Conversation') }} #{{ $conversation['id'] ?? 'Unknown' }}
                                            </h6>
                                            <small class="text-muted">
                                                {{ isset($conversation['created_at']) ? \Carbon\Carbon::parse($conversation['created_at'])->format('M j, Y H:i') : __('Unknown date') }}
                                            </small>
                                        </div>
                                        <div class="card-body">
                                            @if(isset($conversation['messages']) && count($conversation['messages']) > 0)
                                                <div class="conversation-messages" style="max-height: 200px; overflow-y: auto;">
                                                    @foreach(array_slice($conversation['messages'], -3) as $message)
                                                        <div class="message mb-2 {{ $message['role'] === 'user' ? 'text-end' : 'text-start' }}">
                                                            <div class="message-bubble p-2 rounded {{ $message['role'] === 'user' ? 'bg-primary text-white' : 'bg-light' }}">
                                                                <small class="d-block">{{ $message['role'] === 'user' ? __('Customer') : __('Bot') }}</small>
                                                                {{ $message['content'] ?? $message['message'] ?? '' }}
                                                            </div>
                                                            <small class="text-muted">
                                                                {{ isset($message['timestamp']) ? \Carbon\Carbon::parse($message['timestamp'])->format('H:i') : '' }}
                                                            </small>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @else
                                                <p class="text-muted">{{ __('No messages in this conversation') }}</p>
                                            @endif
                                            
                                            <hr>
                                            
                                            <div class="conversation-stats">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <small class="text-muted">{{ __('Messages') }}</small>
                                                        <div class="fw-bold">{{ isset($conversation['messages']) ? count($conversation['messages']) : 0 }}</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">{{ __('Status') }}</small>
                                                        <div class="fw-bold">
                                                            @if(isset($conversation['status']))
                                                                @if($conversation['status'] === 'active')
                                                                    <span class="badge bg-success">{{ __('Active') }}</span>
                                                                @elseif($conversation['status'] === 'ended')
                                                                    <span class="badge bg-secondary">{{ __('Ended') }}</span>
                                                                @else
                                                                    <span class="badge bg-warning">{{ $conversation['status'] }}</span>
                                                                @endif
                                                            @else
                                                                <span class="badge bg-secondary">{{ __('Unknown') }}</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @if(isset($conversation['id']))
                                            <div class="card-footer">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#sendMessageModal" data-conversation-id="{{ $conversation['id'] }}">
                                                    <i class="fas fa-reply"></i> {{ __('Send Message') }}
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination would go here if supported by API -->
                        @if(count($conversations) >= 50)
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                {{ __('Showing the most recent 50 conversations. Older conversations may be available through the Chatbase dashboard.') }}
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Send Message Modal -->
    <div class="modal fade" id="sendMessageModal" tabindex="-1" aria-labelledby="sendMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sendMessageModalLabel">{{ __('Send Message') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="sendMessageForm" action="{{ route('marketplace.vendor.chatbot.conversations.send-message', $agent->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <input type="hidden" name="conversation_id" id="conversationId">
                        <div class="mb-3">
                            <label for="message" class="form-label">{{ __('Message') }}</label>
                            <textarea class="form-control" id="message" name="message" rows="4" required placeholder="{{ __('Type your message here...') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> {{ __('Send Message') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle send message modal
    const sendMessageModal = document.getElementById('sendMessageModal');
    if (sendMessageModal) {
        sendMessageModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const conversationId = button.getAttribute('data-conversation-id');
            const conversationIdInput = sendMessageModal.querySelector('#conversationId');
            conversationIdInput.value = conversationId;
        });
    }

    // Handle form submission
    const sendMessageForm = document.getElementById('sendMessageForm');
    if (sendMessageForm) {
        sendMessageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {{ __("Sending...") }}';
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('{{ __("Error") }}: ' + (data.message || '{{ __("Failed to send message") }}'));
                } else {
                    alert('{{ __("Message sent successfully!") }}');
                    const modal = bootstrap.Modal.getInstance(sendMessageModal);
                    modal.hide();
                    this.reset();
                    // Optionally reload the page to show updated conversation
                    // window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __("An error occurred while sending the message") }}');
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
        });
    }
});
</script>
@endpush
