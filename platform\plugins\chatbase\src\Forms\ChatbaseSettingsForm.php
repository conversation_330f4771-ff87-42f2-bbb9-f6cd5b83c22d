<?php

namespace Bo<PERSON>ble\Chatbase\Forms;

use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\Fields\OnOffCheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\FormAbstract;
use Botble\Chatbase\Http\Requests\ChatbaseSettingsRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Marketplace\Facades\MarketplaceHelper;

class ChatbaseSettingsForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->template(MarketplaceHelper::viewPath('vendor-dashboard.forms.base'))
            ->setValidatorClass(ChatbaseSettingsRequest::class)
            ->add(
                'instructions',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.instructions'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.instructions_helper'))
                    ->rows(4)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.instructions_placeholder'),
                    ])
            )
            ->add(
                'initial_messages',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.initial_messages'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.initial_messages_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.initial_messages_placeholder'),
                    ])
            )
            ->add(
                'suggested_messages',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.suggested_messages'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.suggested_messages_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.suggested_messages_placeholder'),
                    ])
            )
            ->add(
                'visibility',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.visibility'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.visibility_helper'))
                    ->choices([
                        'private' => trans('plugins/chatbase::chatbase.agent.visibility_private'),
                        'public' => trans('plugins/chatbase::chatbase.agent.visibility_public'),
                    ])
                    ->defaultValue('private')
            )
            // ->add(
            //     'only_allow_on_added_domains',
            //     OnOffCheckboxField::class,
            //     OnOffFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.only_allow_on_added_domains'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.only_allow_on_added_domains_helper'))
            //         ->defaultValue(false)
            // )
            // ->add(
            //     'domains',
            //     TextareaField::class,
            //     TextareaFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.domains'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.domains_helper'))
            //         ->rows(3)
            //         ->attributes([
            //             'placeholder' => trans('plugins/chatbase::chatbase.agent.domains_placeholder'),
            //         ])
            // )
            // ->add(
            //     'ip_limit',
            //     TextField::class,
            //     TextFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.ip_limit'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.ip_limit_helper'))
            //         ->attributes([
            //             'placeholder' => trans('plugins/chatbase::chatbase.agent.ip_limit_placeholder'),
            //             'type' => 'number',
            //             'min' => '0',
            //         ])
            // )
            // ->add(
            //     'ip_limit_timeframe',
            //     TextField::class,
            //     TextFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.ip_limit_timeframe'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.ip_limit_timeframe_helper'))
            //         ->attributes([
            //             'placeholder' => trans('plugins/chatbase::chatbase.agent.ip_limit_timeframe_placeholder'),
            //             'type' => 'number',
            //             'min' => '0',
            //         ])
            // )
            // ->add(
            //     'ip_limit_message',
            //     TextField::class,
            //     TextFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.ip_limit_message'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.ip_limit_message_helper'))
            //         ->attributes([
            //             'placeholder' => trans('plugins/chatbase::chatbase.agent.ip_limit_message_placeholder'),
            //         ])
            // )
            // ->add(
            //     'model',
            //     SelectField::class,
            //     SelectFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.model'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.model_helper'))
            //         ->choices([
            //             'gpt-4o-mini' => 'GPT-4o Mini (Recommended)',
            //             'gpt-4o' => 'GPT-4o',
            //             'gpt-4-turbo' => 'GPT-4 Turbo',
            //             'gpt-4' => 'GPT-4',
            //             'claude-3-5-sonnet' => 'Claude 3.5 Sonnet',
            //             'claude-3-opus' => 'Claude 3 Opus',
            //             'claude-3-haiku' => 'Claude 3 Haiku',
            //             'gemini-1.5-pro' => 'Gemini 1.5 Pro',
            //             'gemini-1.5-flash' => 'Gemini 1.5 Flash',
            //         ])
            //         ->defaultValue(setting('chatbase_default_model', 'gpt-4o-mini'))
            // )
            // ->add(
            //     'temp',
            //     SelectField::class,
            //     SelectFieldOption::make()
            //         ->label(trans('plugins/chatbase::chatbase.agent.temp'))
            //         ->helperText(trans('plugins/chatbase::chatbase.agent.temp_helper'))
            //         ->choices([
            //             '0' => '0 - ' . trans('plugins/chatbase::chatbase.agent.temp_focused'),
            //             '0.3' => '0.3 - ' . trans('plugins/chatbase::chatbase.agent.temp_balanced'),
            //             '0.7' => '0.7 - ' . trans('plugins/chatbase::chatbase.agent.temp_creative'),
            //             '1' => '1 - ' . trans('plugins/chatbase::chatbase.agent.temp_very_creative'),
            //         ])
            //         ->defaultValue('0')
            // )
            ;
    }
}
