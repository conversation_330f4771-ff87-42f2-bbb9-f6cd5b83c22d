@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('plugins/chatbase::chatbase.admin.chatbots.view', ['name' => $agent->name]) }}</h4>
                    <div class="card-header-action">
                        <a href="{{ route('chatbase.admin.chatbots.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left"></i> {{ trans('core/base::forms.back') }}
                        </a>
                        <a href="{{ route('chatbase.admin.chatbots.edit', $agent->id) }}" class="btn btn-primary">
                            <i class="ti ti-edit"></i> {{ trans('core/base::forms.edit') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>{{ $agent->name }}</h5>
                            @if($agent->description)
                                <p class="text-muted">{{ $agent->description }}</p>
                            @endif
                            
                            <div class="row mt-4">
                                <div class="col-sm-6">
                                    <strong>{{ trans('plugins/chatbase::chatbase.admin.chatbot.store') }}:</strong>
                                    @if($agent->store)
                                        <a href="{{ route('marketplace.store.edit', $agent->store->id) }}" target="_blank">
                                            {{ $agent->store->name }}
                                        </a>
                                    @else
                                        <span class="text-muted">{{ trans('core/base::forms.none') }}</span>
                                    @endif
                                </div>
                                <div class="col-sm-6">
                                    <strong>{{ trans('plugins/chatbase::chatbase.admin.chatbot.customer') }}:</strong>
                                    @if($agent->customer)
                                        <a href="{{ route('customers.edit', $agent->customer->id) }}" target="_blank">
                                            {{ $agent->customer->name }}
                                        </a>
                                    @else
                                        <span class="text-muted">{{ trans('core/base::forms.none') }}</span>
                                    @endif
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-sm-6">
                                    <strong>{{ trans('core/base::forms.status') }}:</strong>
                                    @if($agent->status === 'active')
                                        <span class="badge bg-success">{{ trans('plugins/chatbase::chatbase.status.active') }}</span>
                                    @elseif($agent->status === 'creating')
                                        <span class="badge bg-warning">{{ trans('plugins/chatbase::chatbase.status.creating') }}</span>
                                    @elseif($agent->status === 'error')
                                        <span class="badge bg-danger">{{ trans('plugins/chatbase::chatbase.status.error') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ trans('plugins/chatbase::chatbase.status.inactive') }}</span>
                                    @endif
                                </div>
                                <div class="col-sm-6">
                                    <strong>{{ trans('plugins/chatbase::chatbase.admin.chatbot.chatbot_id') }}:</strong>
                                    @if($agent->chatbot_id)
                                        <code>{{ $agent->chatbot_id }}</code>
                                    @else
                                        <span class="text-muted">{{ trans('core/base::forms.none') }}</span>
                                    @endif
                                </div>
                            </div>

                            @if($agent->error_message)
                                <div class="alert alert-danger mt-3">
                                    <strong>{{ trans('core/base::forms.error') }}:</strong> {{ $agent->error_message }}
                                </div>
                            @endif

                            <div class="row mt-3">
                                <div class="col-sm-6">
                                    <strong>{{ trans('plugins/chatbase::chatbase.admin.chatbot.last_trained') }}:</strong>
                                    @if($agent->last_trained_at)
                                        {{ $agent->last_trained_at->format('M j, Y H:i') }}
                                    @else
                                        <span class="text-muted">{{ trans('plugins/chatbase::chatbase.dashboard.never') }}</span>
                                    @endif
                                </div>
                                <div class="col-sm-6">
                                    <strong>{{ trans('core/base::forms.created_at') }}:</strong>
                                    {{ $agent->created_at->format('M j, Y H:i') }}
                                </div>
                            </div>

                            @if($agent->instructions)
                                <div class="mt-4">
                                    <h6>{{ trans('plugins/chatbase::chatbase.agent.instructions') }}</h6>
                                    <div class="bg-light p-3 rounded">
                                        {{ $agent->instructions }}
                                    </div>
                                </div>
                            @endif

                            @if($agent->initial_messages && count($agent->initial_messages) > 0)
                                <div class="mt-4">
                                    <h6>{{ trans('plugins/chatbase::chatbase.agent.initial_messages') }}</h6>
                                    <ul class="list-group">
                                        @foreach($agent->initial_messages as $message)
                                            <li class="list-group-item">{{ $message }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if($agent->suggested_messages && count($agent->suggested_messages) > 0)
                                <div class="mt-4">
                                    <h6>{{ trans('plugins/chatbase::chatbase.agent.suggested_messages') }}</h6>
                                    <ul class="list-group">
                                        @foreach($agent->suggested_messages as $message)
                                            <li class="list-group-item">{{ $message }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">{{ trans('core/base::forms.actions') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('chatbase.admin.chatbots.edit', $agent->id) }}" class="btn btn-primary">
                                            <i class="ti ti-edit"></i> {{ trans('core/base::forms.edit') }}
                                        </a>
                                        
                                        @if($agent->store)
                                            <a href="{{ route('marketplace.store.edit', $agent->store->id) }}" class="btn btn-info" target="_blank">
                                                <i class="ti ti-external-link"></i> {{ trans('plugins/chatbase::chatbase.admin.chatbot.store') }}
                                            </a>
                                        @endif
                                        
                                        @if($agent->customer)
                                            <a href="{{ route('customers.edit', $agent->customer->id) }}" class="btn btn-info" target="_blank">
                                                <i class="ti ti-external-link"></i> {{ trans('plugins/chatbase::chatbase.admin.chatbot.customer') }}
                                            </a>
                                        @endif
                                    </div>
                                    
                                    <hr>
                                    
                                    <form action="{{ route('chatbase.admin.chatbots.destroy', $agent->id) }}" method="POST" 
                                          onsubmit="return confirm('{{ trans('core/base::forms.confirm_delete') }}')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger w-100">
                                            <i class="ti ti-trash"></i> {{ trans('core/base::forms.delete') }}
                                        </button>
                                    </form>
                                </div>
                            </div>

                            @if($agent->chatbot_id)
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">{{ trans('plugins/chatbase::chatbase.widget.loading') }}</h6>
                                    </div>
                                    <div class="card-body">
                                        <textarea class="form-control" rows="4" readonly onclick="this.select()">{{ $agent->getWidgetCode() }}</textarea>
                                        <small class="text-muted">{{ trans('plugins/chatbase::chatbase.dashboard.widget_status') }}</small>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
