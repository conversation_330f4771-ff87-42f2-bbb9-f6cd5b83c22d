<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('chatbase_agents', function (Blueprint $table) {
            // Chatbot settings fields
            $table->text('instructions')->nullable()->after('description');
            $table->json('initial_messages')->nullable()->after('instructions');
            $table->json('suggested_messages')->nullable()->after('initial_messages');
            $table->string('visibility')->default('private')->after('suggested_messages');
            $table->boolean('only_allow_on_added_domains')->default(false)->after('visibility');
            $table->json('domains')->nullable()->after('only_allow_on_added_domains');
            $table->integer('ip_limit')->nullable()->after('domains');
            $table->integer('ip_limit_timeframe')->nullable()->after('ip_limit');
            $table->string('ip_limit_message')->nullable()->after('ip_limit_timeframe');
            $table->string('model')->default('gpt-4o-mini')->after('ip_limit_message');
            $table->decimal('temp', 2, 1)->default(0.0)->after('model');
        });
    }

    public function down(): void
    {
        Schema::table('chatbase_agents', function (Blueprint $table) {
            $table->dropColumn([
                'instructions',
                'initial_messages',
                'suggested_messages',
                'visibility',
                'only_allow_on_added_domains',
                'domains',
                'ip_limit',
                'ip_limit_timeframe',
                'ip_limit_message',
                'model',
                'temp',
            ]);
        });
    }
};
