<?php

namespace Bo<PERSON>ble\Chatbase\Tests\Feature;

use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Chatbase\Services\ChatbaseAgentService;
use Bo<PERSON>ble\Chatbase\Services\ChatbaseApiService;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ChatbaseIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected Customer $customer;
    protected Store $store;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test customer and store
        $this->customer = Customer::factory()->create([
            'is_vendor' => true,
        ]);

        $this->store = Store::factory()->create([
            'customer_id' => $this->customer->id,
            'name' => 'Test Store',
            'description' => 'A test store for Chatbase integration',
        ]);

        // Set test API key
        setting(['chatbase_api_key' => 'test-api-key']);
    }

    public function test_can_create_chatbase_agent()
    {
        // Mock successful API response
        Http::fake([
            'www.chatbase.co/api/v1/create-chatbot' => Http::response([
                'chatbotId' => 'test-chatbot-123',
            ], 200),
        ]);

        $agentService = app(ChatbaseAgentService::class);

        $result = $agentService->createAgentForStore($this->store, [
            'name' => 'Test Agent',
            'description' => 'Test agent description',
            'training_text' => 'This is test training content.',
        ]);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(ChatbaseAgent::class, $result['agent']);
        $this->assertEquals('test-chatbot-123', $result['chatbot_id']);

        // Verify agent was saved to database
        $this->assertDatabaseHas('chatbase_agents', [
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'chatbot_id' => 'test-chatbot-123',
            'status' => 'active',
        ]);
    }

    public function test_cannot_create_duplicate_agent()
    {
        // Create existing agent
        ChatbaseAgent::create([
            'name' => 'Existing Agent',
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'status' => 'active',
        ]);

        $agentService = app(ChatbaseAgentService::class);

        $result = $agentService->createAgentForStore($this->store, [
            'name' => 'New Agent',
        ]);

        $this->assertFalse($result['success']);
        $this->assertStringContains('already exists', $result['error']);
    }

    public function test_api_service_handles_errors()
    {
        // Mock API error response
        Http::fake([
            'www.chatbase.co/api/v1/create-chatbot' => Http::response([
                'message' => 'Invalid API key',
            ], 401),
        ]);

        $apiService = app(ChatbaseApiService::class);

        $result = $apiService->createChatbot('Test Bot', 'Test content');

        $this->assertFalse($result['success']);
        $this->assertStringContains('Invalid API key', $result['error']);
    }

    public function test_agent_widget_code_generation()
    {
        $agent = ChatbaseAgent::create([
            'name' => 'Test Agent',
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'chatbot_id' => 'test-chatbot-123',
            'status' => 'active',
            'settings' => [
                'widget' => [
                    'enabled' => true,
                    'position' => 'bottom-right',
                    'theme' => 'light',
                ],
            ],
        ]);

        $widgetCode = $agent->getWidgetCode();

        $this->assertStringContains('test-chatbot-123', $widgetCode);
        $this->assertStringContains('www.chatbase.co/embed.min.js', $widgetCode);
        $this->assertStringContains('embeddedChatbotConfig', $widgetCode);
    }

    public function test_agent_status_methods()
    {
        $agent = ChatbaseAgent::create([
            'name' => 'Test Agent',
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'chatbot_id' => 'test-chatbot-123',
            'status' => 'active',
        ]);

        $this->assertTrue($agent->isActive());
        $this->assertFalse($agent->isCreating());
        $this->assertFalse($agent->hasError());

        $agent->update(['status' => 'creating']);
        $this->assertFalse($agent->isActive());
        $this->assertTrue($agent->isCreating());

        $agent->update(['status' => 'error']);
        $this->assertTrue($agent->hasError());
    }

    public function test_training_source_character_counting()
    {
        $agent = ChatbaseAgent::create([
            'name' => 'Test Agent',
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'status' => 'draft',
        ]);

        $source = $agent->trainingSources()->create([
            'type' => 'text',
            'title' => 'Test Content',
            'content' => 'This is test content for character counting.',
            'status' => 'active',
        ]);

        $source->updateCharacterCount();

        $this->assertEquals(strlen('This is test content for character counting.'), $source->character_count);
        $this->assertEquals($source->character_count, $agent->getTotalCharacterCount());
    }
}
