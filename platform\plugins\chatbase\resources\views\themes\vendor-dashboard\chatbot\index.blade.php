@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ __('Chatbot Management') }}</h4>
                    <div class="card-header-action">
                        @if(!$agent)
                            <a href="{{ route('marketplace.vendor.chatbot.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> {{ __('Create Chatbot') }}
                            </a>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    @if($agent)
                        <div class="row">
                            <div class="col-md-8">
                                <h5>{{ $agent->name }}</h5>
                                @if($agent->description)
                                    <p class="text-muted">{{ $agent->description }}</p>
                                @endif
                                
                                <div class="row mt-3">
                                    <div class="col-sm-6">
                                        <strong>{{ __('Status') }}:</strong>
                                        @if($agent->status === 'active')
                                            <span class="badge bg-success">{{ __('Active') }}</span>
                                        @elseif($agent->status === 'creating')
                                            <span class="badge bg-warning">{{ __('Creating') }}</span>
                                        @elseif($agent->status === 'error')
                                            <span class="badge bg-danger">{{ __('Error') }}</span>
                                        @else
                                            <span class="badge bg-secondary">{{ __('Inactive') }}</span>
                                        @endif
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>{{ __('Chatbot ID') }}:</strong>
                                        @if($agent->chatbot_id)
                                            <code>{{ $agent->chatbot_id }}</code>
                                        @else
                                            <span class="text-muted">{{ __('Not created yet') }}</span>
                                        @endif
                                    </div>
                                </div>

                                @if($agent->error_message)
                                    <div class="alert alert-danger mt-3">
                                        <strong>{{ __('Error') }}:</strong> {{ $agent->error_message }}
                                    </div>
                                @endif

                                <div class="row mt-3">
                                    <div class="col-sm-6">
                                        <strong>{{ __('Last Trained') }}:</strong>
                                        @if($agent->last_trained_at)
                                            {{ $agent->last_trained_at->format('M j, Y H:i') }}
                                        @else
                                            <span class="text-muted">{{ __('Never') }}</span>
                                        @endif
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>{{ __('Last Synced') }}:</strong>
                                        @if($agent->last_synced_at)
                                            {{ $agent->last_synced_at->format('M j, Y H:i') }}
                                        @else
                                            <span class="text-muted">{{ __('Never') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-end">
                                    @if($agent->chatbot_id)
                                        <div class="btn-group-vertical d-grid gap-2">
                                            <a href="{{ route('marketplace.vendor.chatbot.edit', $agent->id) }}" class="btn btn-primary">
                                                <i class="fas fa-edit"></i> {{ __('Edit Chatbot') }}
                                            </a>
                                            <a href="{{ route('marketplace.vendor.chatbot.settings.edit', $agent->id) }}" class="btn btn-info">
                                                <i class="fas fa-cog"></i> {{ __('Settings') }}
                                            </a>
                                            <a href="{{ route('marketplace.vendor.chatbot.media.index', $agent->id) }}" class="btn btn-secondary">
                                                <i class="fas fa-image"></i> {{ __('Media') }}
                                            </a>
                                            <a href="{{ route('marketplace.vendor.chatbot.analytics', $agent->id) }}" class="btn btn-success">
                                                <i class="fas fa-chart-bar"></i> {{ __('Analytics') }}
                                            </a>
                                        </div>
                                        
                                        <hr>
                                        
                                        <form action="{{ route('marketplace.vendor.chatbot.destroy', $agent->id) }}" method="POST" 
                                              onsubmit="return confirm('{{ __('Are you sure you want to delete this chatbot? This action cannot be undone.') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger w-100">
                                                <i class="fas fa-trash"></i> {{ __('Delete Chatbot') }}
                                            </button>
                                        </form>
                                    @else
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            {{ __('Chatbot is being created. Please wait...') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($agent->chatbot_id)
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h6>{{ __('Widget Preview') }}</h6>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>{{ __('Embed Code') }}:</strong></p>
                                        <textarea class="form-control" rows="4" readonly onclick="this.select()">{{ $agent->getWidgetCode() }}</textarea>
                                        <small class="text-muted">{{ __('Copy this code and paste it into your website to embed the chatbot widget.') }}</small>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                            <h5>{{ __('No Chatbot Created') }}</h5>
                            <p class="text-muted">{{ __('Create your first chatbot to start engaging with your customers.') }}</p>
                            <a href="{{ route('marketplace.vendor.chatbot.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> {{ __('Create Chatbot') }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
