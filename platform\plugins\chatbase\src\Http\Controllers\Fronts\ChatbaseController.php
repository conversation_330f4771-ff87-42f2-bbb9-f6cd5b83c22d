<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\ChatbaseAgentForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\ChatbaseAgentRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseAgentService;
use Botble\Chatbase\Forms\ChatbaseSettingsForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\ChatbaseSettingsRequest;

use Illuminate\Http\Request;

class ChatbaseController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle(__('Chatbase Agent'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('store_id', $store->id)->first();

        $data = compact('user', 'store', 'agent');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => view('plugins/chatbase::themes.vendor-dashboard.chatbase.index', $data)->render(),
                ]);
        }

        return view('plugins/chatbase::themes.vendor-dashboard.chatbase.index', $data);
    }

    public function create()
    {
        $this->pageTitle(__('Create Chatbase Agent'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        // Check if agent already exists
        $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
        if ($existingAgent) {
            return redirect()
                ->route('marketplace.vendor.chatbase.index')
                ->with('error_msg', __('Agent already exists for your store'));
        }

        return ChatbaseAgentForm::create()
            ->setUrl(route('marketplace.vendor.chatbase.store'))
            ->renderForm();
    }

    public function store(ChatbaseAgentRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        // Check if agent already exists
        $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
        if ($existingAgent) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Agent already exists for your store'));
        }

        $result = $agentService->createAgentForStore($store, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbase.index'))
            ->setMessage(__('Agent created successfully!'));
    }

    public function edit(string $id)
    {
        $this->pageTitle(__('Edit Chatbase Agent'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        return ChatbaseAgentForm::createFromModel($agent)
            ->setUrl(route('marketplace.vendor.chatbase.update', $agent->id))
            ->renderForm();
    }

    public function settings(string $id)
    {
        $this->pageTitle(__('Chatbot Settings'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        return ChatbaseSettingsForm::createFromModel($agent)
            ->setUrl(route('marketplace.vendor.chatbase.update-settings', $agent->id))
            ->renderForm();
    }

    public function updateSettings(string $id, ChatbaseSettingsRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->updateAgentSettings($agent, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbase.index'))
            ->setMessage(__('Chatbot settings updated successfully!'));
    }

    public function update(string $id, ChatbaseAgentRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->updateAgent($agent, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbase.index'))
            ->setMessage(__('Agent updated successfully!'));
    }

    public function destroy(string $id, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->deleteAgent($agent);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setMessage(__('Agent deleted successfully!'));
    }
}
