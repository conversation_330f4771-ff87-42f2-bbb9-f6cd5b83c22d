# Chatbase Plugin Setup Verification

## Quick Setup Steps

1. **Clear Application Cache**:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   ```

2. **Run Migrations**:
   ```bash
   php artisan migrate
   ```

3. **Activate Plugin**:
   - Go to Admin Panel > Plugins
   - Find "Chatbase Integration"
   - Click "Activate"

4. **Verify Settings Menu**:
   - Go to Admin Panel > Settings
   - Look for "Chatbase Integration" in the "Other" section
   - Click on it to access the settings page

## Expected Settings Page Features

The Chatbase settings page should include:

- **Chatbase API Key** field
- **Enable Chatbase Widget** toggle
- **Auto-embed Widget** toggle  
- **Default AI Model** dropdown
- **Default Widget Position** dropdown
- **Default Widget Theme** dropdown
- **Default Instructions** textarea

## Testing the Configuration

1. **API Key Test**:
   - Enter a test API key
   - Save settings
   - The system should validate the API key automatically

2. **Vendor Dashboard Test**:
   - Login as a vendor
   - Go to Vendor Dashboard
   - Look for "Chatbase Agent" menu item
   - Click to access agent management

## Troubleshooting

If the settings don't appear:

1. **Check Plugin Activation**:
   ```bash
   php artisan plugin:list
   ```

2. **Clear All Caches**:
   ```bash
   php artisan optimize:clear
   ```

3. **Check Permissions**:
   - Ensure user has 'chatbase.settings' permission
   - Check in Admin Panel > Users > Roles

4. **Check Logs**:
   ```bash
   tail -f storage/logs/laravel.log
   ```

## Success Indicators

✅ Settings appear in Settings > Other section
✅ API key validation works
✅ Vendor menu shows "Chatbase Agent"
✅ No errors in Laravel logs
✅ Database tables created successfully
