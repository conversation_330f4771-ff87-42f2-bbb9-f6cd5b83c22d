<?php

namespace Botble\Chatbase\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\Settings\ChatbaseSettingForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\Settings\ChatbaseSettingRequest;
use Bo<PERSON>ble\Chatbase\Services\ChatbaseApiService;
use Botble\Setting\Http\Controllers\SettingController;

class ChatbaseSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/chatbase::chatbase.settings.title'));

        return ChatbaseSettingForm::create()->renderForm();
    }

    public function update(ChatbaseSettingRequest $request): BaseHttpResponse
    {
        $data = $request->validated();

        // // Test API connection if API key is provided
        if (!empty($data['chatbase_api_key'])) {
            $apiService = new ChatbaseApiService();

            // Temporarily set the API key for testing
            // Set API key in both config and settings
            config(['plugins.chatbase.general.api_key' => $data['chatbase_api_key']]);

            $testResult = $apiService->testConnection();
            // dd($testResult);
            if (!$testResult['success']) {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setMessage(trans('plugins/chatbase::chatbase.settings.api_key_invalid', [
                        'error' => $testResult['error']
                    ]));
            }
        }

        return $this->performUpdate($data);
    }
}
