<?php

namespace Botble\Chatbase\Http\Requests\Admin;

use Botble\Support\Http\Requests\Request;

class AdminChatbotRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'training_text' => ['nullable', 'string', 'max:50000'],
            'instructions' => ['nullable', 'string', 'max:2000'],
            'initial_messages' => ['nullable', 'string', 'max:1000'],
            'suggested_messages' => ['nullable', 'string', 'max:1000'],
            'visibility' => ['nullable', 'string', 'in:private,public'],
            'only_allow_on_added_domains' => ['nullable', 'boolean'],
            'domains' => ['nullable', 'string', 'max:1000'],
            'ip_limit' => ['nullable', 'integer', 'min:0'],
            'ip_limit_timeframe' => ['nullable', 'integer', 'min:0'],
            'ip_limit_message' => ['nullable', 'string', 'max:255'],
            'model' => ['nullable', 'string', 'in:gpt-4o-mini,gpt-4o,gpt-4-turbo,gpt-4,claude-3-5-sonnet,claude-3-opus,claude-3-haiku,gemini-1.5-pro,gemini-1.5-flash'],
            'temp' => ['nullable', 'numeric', 'min:0', 'max:2'],
        ];

        // Store ID is required for creation, not for updates
        if ($this->isMethod('POST')) {
            $rules['store_id'] = ['required', 'exists:mp_stores,id'];
        }

        return $rules;
    }

    public function attributes(): array
    {
        return [
            'store_id' => trans('plugins/chatbase::chatbase.admin.chatbot.store'),
            'name' => trans('plugins/chatbase::chatbase.agent.name'),
            'description' => trans('plugins/chatbase::chatbase.agent.description'),
            'training_text' => trans('plugins/chatbase::chatbase.agent.training_text'),
            'instructions' => trans('plugins/chatbase::chatbase.agent.instructions'),
            'initial_messages' => trans('plugins/chatbase::chatbase.agent.initial_messages'),
            'suggested_messages' => trans('plugins/chatbase::chatbase.agent.suggested_messages'),
            'visibility' => trans('plugins/chatbase::chatbase.agent.visibility'),
            'only_allow_on_added_domains' => trans('plugins/chatbase::chatbase.agent.only_allow_on_added_domains'),
            'domains' => trans('plugins/chatbase::chatbase.agent.domains'),
            'ip_limit' => trans('plugins/chatbase::chatbase.agent.ip_limit'),
            'ip_limit_timeframe' => trans('plugins/chatbase::chatbase.agent.ip_limit_timeframe'),
            'ip_limit_message' => trans('plugins/chatbase::chatbase.agent.ip_limit_message'),
            'model' => trans('plugins/chatbase::chatbase.agent.model'),
            'temp' => trans('plugins/chatbase::chatbase.agent.temp'),
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'only_allow_on_added_domains' => $this->boolean('only_allow_on_added_domains'),
        ]);
    }

    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // Prepare settings for storage
        $settings = [
            'model' => $validated['model'] ?? setting('chatbase_default_model', 'gpt-4o-mini'),
            'temp' => $validated['temp'] ?? 0.0,
            'widget' => [
                'enabled' => true,
                'position' => setting('chatbase_widget_position', 'bottom-right'),
                'theme' => setting('chatbase_widget_theme', 'light'),
            ],
        ];

        $validated['settings'] = $settings;

        // Prepare API settings for Chatbase
        $apiSettings = [
            'instructions' => $validated['instructions'] ?? null,
            'initialMessages' => $this->parseArrayField($validated['initial_messages'] ?? ''),
            'suggestedMessages' => $this->parseArrayField($validated['suggested_messages'] ?? ''),
            'visibility' => $validated['visibility'] ?? 'private',
            'onlyAllowOnAddedDomains' => $validated['only_allow_on_added_domains'] ?? false,
            'domains' => $this->parseArrayField($validated['domains'] ?? ''),
            'ipLimit' => $validated['ip_limit'] ?? null,
            'ipLimitTimeframe' => $validated['ip_limit_timeframe'] ?? null,
            'ipLimitMessage' => $validated['ip_limit_message'] ?? null,
            'model' => $validated['model'] ?? 'gpt-4o-mini',
            'temp' => $validated['temp'] ?? 0.0,
        ];

        $validated['api_settings'] = array_filter($apiSettings, function ($value) {
            return $value !== null && $value !== '';
        });

        return $validated;
    }

    protected function parseArrayField(string $value): array
    {
        if (empty($value)) {
            return [];
        }

        // Split by newlines and filter empty lines
        $lines = array_filter(array_map('trim', explode("\n", $value)));

        return array_values($lines);
    }
}
