{"__meta": {"id": "01JYSJMVYT4KANNMDDK4YVMYZK", "datetime": "2025-06-27 20:53:12", "utime": **********.283272, "method": "GET", "uri": "/vendor/chatbase/1/analytics", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[20:53:11] LOG.error: Route [marketplace.vendor.chatbase.training] not defined. {\n    \"view\": {\n        \"view\": \"D:\\\\laragon\\\\www\\\\muhrak\\\\platform\\\\plugins\\\\chatbase\\\\resources\\\\views\\\\themes\\\\vendor-dashboard\\\\chatbase\\\\analytics.blade.php\",\n        \"data\": {\n            \"errors\": \"<pre class=sf-dump id=sf-dump-1178652312 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Support\\\\ViewErrorBag<\\/span> {<a class=sf-dump-ref>#4476<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">bags<\\/span>: []\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1178652312\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"user\": \"<pre class=sf-dump id=sf-dump-207953655 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Botble\\\\Ecommerce\\\\Models\\\\Customer<\\/span> {<a class=sf-dump-ref>#4527<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"12 characters\\\">ec_customers<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:20<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">Hironic Vendor<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>password<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"60 characters\\\">$2y$12$FQ50j\\/6Ixi48MRWxJzy1mO4gMqPc.\\/48zhHMyx12TV2y0102AtOr6<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>avatar<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>dob<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">2025-06-26<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>phone<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>remember_token<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"60 characters\\\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 20:24:55<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>confirmed_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 20:24:55<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email_verify_token<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">activated<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>is_verified<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>block_reason<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>private_notes<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>is_vendor<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>vendor_verified_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>stripe_account_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>stripe_account_active<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:20<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">Hironic Vendor<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>password<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"60 characters\\\">$2y$12$FQ50j\\/6Ixi48MRWxJzy1mO4gMqPc.\\/48zhHMyx12TV2y0102AtOr6<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>avatar<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>dob<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">2025-06-26<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>phone<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>remember_token<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"60 characters\\\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 20:24:55<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>confirmed_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 20:24:55<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email_verify_token<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">activated<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>is_verified<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>block_reason<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>private_notes<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>is_vendor<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>vendor_verified_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>stripe_account_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>stripe_account_active<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:3<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"41 characters\\\">Botble\\\\Ecommerce\\\\Enums\\\\CustomerStatusEnum<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>dob<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">date<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>is_verified<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>store<\\/span>\\\" => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"Botble\\\\Marketplace\\\\Models\\\\Store\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">Botble\\\\Marketplace\\\\Models<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">Store<\\/span><\\/span> {<a class=sf-dump-ref>#2227<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"9 characters\\\">mp_stores<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:24<\\/span> [ &#8230;24]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:24<\\/span> [ &#8230;24]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:7<\\/span> [ &#8230;7]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:20<\\/span> [ &#8230;20]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: <span class=sf-dump-note>array:2<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">password<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">remember_token<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">name<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">email<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">password<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">avatar<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">phone<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">private_notes<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">is_verified<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">authPasswordName<\\/span>: \\\"<span class=sf-dump-str title=\\\"8 characters\\\">password<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">rememberTokenName<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">remember_token<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">accessToken<\\/span>: <span class=sf-dump-const>null<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-207953655\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"store\": \"<pre class=sf-dump id=sf-dump-1753571604 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Botble\\\\Marketplace\\\\Models\\\\Store<\\/span> {<a class=sf-dump-ref>#2227<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"9 characters\\\">mp_stores<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:24<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Hironic<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\"><EMAIL>.345<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>phone<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>address<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>state<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>city<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>customer_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>logo<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"17 characters\\\">ff78a236d1ffa.jpg<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>logo_square<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>cover_image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">unnamed-9.jpg<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"126 characters\\\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>content<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">published<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>vendor_verified_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 17:17:01<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>zip_code<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>company<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>tax_id<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>certificate_file<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>government_id_file<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>main_type<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:24<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Hironic<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\"><EMAIL>.345<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>phone<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>address<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>state<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>city<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>customer_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>logo<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"17 characters\\\">ff78a236d1ffa.jpg<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>logo_square<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>cover_image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">unnamed-9.jpg<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"126 characters\\\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>content<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">published<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>vendor_verified_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-13 17:17:01<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 21:27:47<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>zip_code<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>company<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>tax_id<\\/span>\\\" => \\\"\\\"\\n    \\\"<span class=sf-dump-key>certificate_file<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>government_id_file<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>main_type<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:7<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"40 characters\\\">Botble\\\\Marketplace\\\\Enums\\\\StoreStatusEnum<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>main_type<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"35 characters\\\">Botble\\\\Ecommerce\\\\Enums\\\\MainTypeEnum<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">Botble\\\\Base\\\\Casts\\\\SafeContent<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">Botble\\\\Base\\\\Casts\\\\SafeContent<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>content<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">Botble\\\\Base\\\\Casts\\\\SafeContent<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>address<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">Botble\\\\Base\\\\Casts\\\\SafeContent<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>company<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">Botble\\\\Base\\\\Casts\\\\SafeContent<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:20<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">name<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">email<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">phone<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">address<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">country<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">state<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">city<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">customer_id<\\/span>\\\"\\n    <span class=sf-dump-index>8<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">logo<\\/span>\\\"\\n    <span class=sf-dump-index>9<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">logo_square<\\/span>\\\"\\n    <span class=sf-dump-index>10<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">cover_image<\\/span>\\\"\\n    <span class=sf-dump-index>11<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">description<\\/span>\\\"\\n    <span class=sf-dump-index>12<\\/span> => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">content<\\/span>\\\"\\n    <span class=sf-dump-index>13<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n    <span class=sf-dump-index>14<\\/span> => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">company<\\/span>\\\"\\n    <span class=sf-dump-index>15<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">zip_code<\\/span>\\\"\\n    <span class=sf-dump-index>16<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">certificate_file<\\/span>\\\"\\n    <span class=sf-dump-index>17<\\/span> => \\\"<span class=sf-dump-str title=\\\"18 characters\\\">government_id_file<\\/span>\\\"\\n    <span class=sf-dump-index>18<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">tax_id<\\/span>\\\"\\n    <span class=sf-dump-index>19<\\/span> => \\\"<span class=sf-dump-str title=\\\"9 characters\\\">main_type<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1753571604\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"agent\": \"<pre class=sf-dump id=sf-dump-1547687068 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Botble\\\\Chatbase\\\\Models\\\\ChatbaseAgent<\\/span> {<a class=sf-dump-ref>#4562<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"15 characters\\\">chatbase_agents<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:14<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">Hironic Vendor<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">This is agent for the testing<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>store_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>customer_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>chatbot_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\">VzsKZKoybj6Yoj-G744eN<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">active<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>settings<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"125 characters\\\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>training_data<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>error_message<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>last_trained_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>last_synced_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:06:58<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:14<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">Hironic Vendor<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>description<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\">This is agent for the testing<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>store_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>customer_id<\\/span>\\\" => <span class=sf-dump-num>410<\\/span>\\n    \\\"<span class=sf-dump-key>chatbot_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\">VzsKZKoybj6Yoj-G744eN<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">active<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>settings<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"125 characters\\\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>training_data<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>error_message<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>last_trained_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>last_synced_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:06:58<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-27 19:07:02<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:4<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>settings<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>training_data<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>last_trained_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>last_synced_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:11<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">name<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">description<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">store_id<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">customer_id<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">chatbot_id<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">settings<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">training_data<\\/span>\\\"\\n    <span class=sf-dump-index>8<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">error_message<\\/span>\\\"\\n    <span class=sf-dump-index>9<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">last_trained_at<\\/span>\\\"\\n    <span class=sf-dump-index>10<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">last_synced_at<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1547687068\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"conversations\": \"<pre class=sf-dump id=sf-dump-171691092 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>array:1<\\/span> [<samp data-depth=1 class=sf-dump-expanded>\\n  \\\"<span class=sf-dump-key>data<\\/span>\\\" => <span class=sf-dump-note>array:2<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=3 class=sf-dump-compact>\\n      \\\"<span class=sf-dump-key>source<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">Widget or Iframe<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"36 characters\\\">2ee3ab7c-8bf2-427e-9933-7023a14b476e<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>form_submission<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n      \\\"<span class=sf-dump-key>messages<\\/span>\\\" => <span class=sf-dump-note>array:7<\\/span> [ &#8230;7]\\n      \\\"<span class=sf-dump-key>min_score<\\/span>\\\" => <span class=sf-dump-num>9.6856085E-5<\\/span>\\n      \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"32 characters\\\">2025-06-27T20:47:15.167025+00:00<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>last_message_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"32 characters\\\">2025-06-27T20:47:53.138871+00:00<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">Pakistan<\\/span>\\\"\\n    <\\/samp>]\\n    <span class=sf-dump-index>1<\\/span> => <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=3 class=sf-dump-compact>\\n      \\\"<span class=sf-dump-key>source<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">Playground<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"36 characters\\\">33dca671-0a64-4d59-a086-5d4025624554<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>form_submission<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n      \\\"<span class=sf-dump-key>messages<\\/span>\\\" => <span class=sf-dump-note>array:3<\\/span> [ &#8230;3]\\n      \\\"<span class=sf-dump-key>min_score<\\/span>\\\" => <span class=sf-dump-num>0.002800927<\\/span>\\n      \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"32 characters\\\">2025-06-27T19:10:12.409685+00:00<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>last_message_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"32 characters\\\">2025-06-27T19:10:12.409685+00:00<\\/span>\\\"\\n      \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">Pakistan<\\/span>\\\"\\n    <\\/samp>]\\n  <\\/samp>]\\n<\\/samp>]\\n<\\/pre><script>Sfdump(\\\"sf-dump-171691092\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"leads\": \"<pre class=sf-dump id=sf-dump-1773817228 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>array:1<\\/span> [<samp data-depth=1 class=sf-dump-expanded>\\n  \\\"<span class=sf-dump-key>collectedCustomers<\\/span>\\\" => <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>data<\\/span>\\\" => []\\n  <\\/samp>]\\n<\\/samp>]\\n<\\/pre><script>Sfdump(\\\"sf-dump-1773817228\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\"\n        }\n    },\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.9564, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.085869, "end": **********.283288, "duration": 4.197418928146362, "duration_str": "4.2s", "measures": [{"label": "Booting", "start": **********.085869, "relative_start": 0, "end": **********.944941, "relative_end": **********.944941, "duration": 0.****************, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.944953, "relative_start": 0.***************, "end": **********.283291, "relative_end": 3.0994415283203125e-06, "duration": 3.****************, "duration_str": "3.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.965293, "relative_start": 0.****************, "end": **********.978248, "relative_end": **********.978248, "duration": 0.012954950332641602, "duration_str": "12.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.835746, "relative_start": 3.****************, "end": **********.283299, "relative_end": 1.0967254638671875e-05, "duration": 0.****************, "duration_str": "448ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.839249, "relative_start": 3.****************, "end": **********.839249, "relative_end": **********.839249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.219353, "relative_start": 4.13348388671875, "end": **********.219353, "relative_end": **********.219353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.223728, "relative_start": 4.137858867645264, "end": **********.223728, "relative_end": **********.223728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.226433, "relative_start": 4.14056396484375, "end": **********.226433, "relative_end": **********.226433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.22899, "relative_start": 4.143121004104614, "end": **********.22899, "relative_end": **********.22899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.249776, "relative_start": 4.163906812667847, "end": **********.249776, "relative_end": **********.249776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.252799, "relative_start": 4.1669299602508545, "end": **********.252799, "relative_end": **********.252799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.255393, "relative_start": 4.1695239543914795, "end": **********.255393, "relative_end": **********.255393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "start": **********.257866, "relative_start": 4.171996831893921, "end": **********.257866, "relative_end": **********.257866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Route [marketplace.vendor.chatbase.training] not defined. (View: D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\resources\\views\\themes\\vendor-dashboard\\chatbase\\analytics.blade.php)", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php", "line": 517, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:76</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">[object Symfony\\Component\\Routing\\Exception\\RouteNotFoundException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">D:\\laragon\\www\\muhrak\\storage\\framework\\views/e7a33947db911c514182339d94e266f0.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Shortcode\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Shortcode\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21462 title=\"2 occurrences\">#1462</a> &#8230;27}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24476 title=\"2 occurrences\">#4476</a><samp data-depth=5 id=sf-dump-*********-ref24476 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Customer\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24527 title=\"2 occurrences\">#4527</a><samp data-depth=5 id=sf-dump-*********-ref24527 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">ec_customers</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-26</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">activated</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>block_reason</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_vendor</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stripe_account_active</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-26</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">activated</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>block_reason</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_vendor</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stripe_account_active</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Botble\\Ecommerce\\Enums\\CustomerStatusEnum</span>\"\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"3 occurrences\">#2227</a><samp data-depth=7 id=sf-dump-*********-ref22227 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">mp_stores</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hironic</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.345</span>\"\n                \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>address</span>\" => \"\"\n                \"<span class=sf-dump-key>country</span>\" => \"\"\n                \"<span class=sf-dump-key>state</span>\" => \"\"\n                \"<span class=sf-dump-key>city</span>\" => \"\"\n                \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"17 characters\">ff78a236d1ffa.jpg</span>\"\n                \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">unnamed-9.jpg</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"126 characters\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n                \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n                \"<span class=sf-dump-key>company</span>\" => \"\"\n                \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n                \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hironic</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.345</span>\"\n                \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>address</span>\" => \"\"\n                \"<span class=sf-dump-key>country</span>\" => \"\"\n                \"<span class=sf-dump-key>state</span>\" => \"\"\n                \"<span class=sf-dump-key>city</span>\" => \"\"\n                \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"17 characters\">ff78a236d1ffa.jpg</span>\"\n                \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">unnamed-9.jpg</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"126 characters\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n                \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n                \"<span class=sf-dump-key>company</span>\" => \"\"\n                \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n                \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Botble\\Marketplace\\Enums\\StoreStatusEnum</span>\"\n                \"<span class=sf-dump-key>main_type</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Botble\\Ecommerce\\Enums\\MainTypeEnum</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"7 characters\">country</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">state</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">logo</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">logo_square</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"11 characters\">cover_image</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">zip_code</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">certificate_file</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">government_id_file</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"6 characters\">tax_id</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"9 characters\">main_type</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">avatar</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">is_verified</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"3 occurrences\">#2227</a>}\n        \"<span class=sf-dump-key>agent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Chatbase\\Models\\ChatbaseAgent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Chatbase\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ChatbaseAgent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24562 title=\"2 occurrences\">#4562</a><samp data-depth=5 id=sf-dump-*********-ref24562 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">chatbase_agents</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">This is agent for the testing</span>\"\n            \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>chatbot_id</span>\" => \"<span class=sf-dump-str title=\"21 characters\">VzsKZKoybj6Yoj-G744eN</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"125 characters\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:06:58</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">This is agent for the testing</span>\"\n            \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>chatbot_id</span>\" => \"<span class=sf-dump-str title=\"21 characters\">VzsKZKoybj6Yoj-G744eN</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"125 characters\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:06:58</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">chatbot_id</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">training_data</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"15 characters\">last_trained_at</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">last_synced_at</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>conversations</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Widget or Iframe</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ee3ab7c-8bf2-427e-9933-7023a14b476e</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e048ce11-1b15-46f3-b8e5-3afd2ad661f3</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"12 characters\">who are you?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f18b9473-c484-430d-8dcc-0b480870bfeb</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.012970387</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">3b7bba8f-3572-4ac0-9000-44b09ce48d15</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"175 characters\">I&#039;m an AI chatbot here to assist you with your inquiries, issues, and requests related to aesthetic medical devices, particularly those from Hironic. How can I help you today?</span>\"\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ad81183b-8942-47b7-905e-9f05dd2f64a5</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"30 characters\">how many products do you have?</span>\"\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d9dc994d-f445-4148-9898-5d3fe77a055e</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.00012731104</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4977822c-5b13-48d4-9e54-7528e06f6bf1</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"258 characters\">I can provide information about Hironic&#039;s products, such as the DOUBLO-Gold, which is a beauty equipment device known for its non-invasive facelift and skin rejuvenation features. If you have specific questions about this product or others, feel free to ask!</span>\"\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bfee1c6b-ec3d-4af5-8372-0508e748858c</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ok thanks</span>\"\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">048f2b8b-6f17-4622-908f-53b55d7e73c2</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">95c4ea8e-0c20-4d14-b618-d18e25758325</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"116 characters\">You&#039;re welcome! If you have any more questions or need further assistance, feel free to reach out. Have a great day!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:15.167025+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:53.138871+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Playground</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">33dca671-0a64-4d59-a086-5d4025624554</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ee4e3af5-273a-4e28-af4c-6fcfcc3b0da8</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"25 characters\">hi, what is your purpose?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df297566-8325-493b-987c-c789c28b0718</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.002800927</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6bffb11b-b27d-4026-8472-cf66a2eca655</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"231 characters\">Hello! My purpose is to assist you with any inquiries, issues, or requests you might have, especially related to the Hironic Store and its offerings. If you have any questions or need assistance, feel free to ask. I&#039;m here to help!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>0.002800927</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>collectedCustomers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>data</span>\" => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Shortcode\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Shortcode\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21462 title=\"2 occurrences\">#1462</a> &#8230;27}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24476 title=\"2 occurrences\">#4476</a>}\n        \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Customer\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24527 title=\"2 occurrences\">#4527</a>}\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"3 occurrences\">#2227</a>}\n        \"<span class=sf-dump-key>agent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Chatbase\\Models\\ChatbaseAgent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Chatbase\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ChatbaseAgent</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24562 title=\"2 occurrences\">#4562</a>}\n        \"<span class=sf-dump-key>conversations</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Widget or Iframe</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ee3ab7c-8bf2-427e-9933-7023a14b476e</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e048ce11-1b15-46f3-b8e5-3afd2ad661f3</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"12 characters\">who are you?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f18b9473-c484-430d-8dcc-0b480870bfeb</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.012970387</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">3b7bba8f-3572-4ac0-9000-44b09ce48d15</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"175 characters\">I&#039;m an AI chatbot here to assist you with your inquiries, issues, and requests related to aesthetic medical devices, particularly those from Hironic. How can I help you today?</span>\"\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ad81183b-8942-47b7-905e-9f05dd2f64a5</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"30 characters\">how many products do you have?</span>\"\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d9dc994d-f445-4148-9898-5d3fe77a055e</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.00012731104</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4977822c-5b13-48d4-9e54-7528e06f6bf1</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"258 characters\">I can provide information about Hironic&#039;s products, such as the DOUBLO-Gold, which is a beauty equipment device known for its non-invasive facelift and skin rejuvenation features. If you have specific questions about this product or others, feel free to ask!</span>\"\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bfee1c6b-ec3d-4af5-8372-0508e748858c</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ok thanks</span>\"\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">048f2b8b-6f17-4622-908f-53b55d7e73c2</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">95c4ea8e-0c20-4d14-b618-d18e25758325</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"116 characters\">You&#039;re welcome! If you have any more questions or need further assistance, feel free to reach out. Have a great day!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:15.167025+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:53.138871+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Playground</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">33dca671-0a64-4d59-a086-5d4025624554</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ee4e3af5-273a-4e28-af4c-6fcfcc3b0da8</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"25 characters\">hi, what is your purpose?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df297566-8325-493b-987c-c789c28b0718</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.002800927</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6bffb11b-b27d-4026-8472-cf66a2eca655</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"231 characters\">Hello! My purpose is to assist you with any inquiries, issues, or requests you might have, especially related to the Hironic Store and its offerings. If you have any questions or need assistance, feel free to ask. I&#039;m here to help!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>0.002800927</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>collectedCustomers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>data</span>\" => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"45 characters\">platform/packages/shortcode/src/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Botble\\Shortcode\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">platform/plugins/marketplace/src/Http/Middleware/RedirectIfNotVendor.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Botble\\Marketplace\\Http\\Middleware\\RedirectIfNotVendor</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Botble\\Base\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"74 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">platform/core/base/src/Http/Middleware/HttpsProtocolMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">platform/core/base/src/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Botble\\Base\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">platform/packages/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Botble\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $url;\n", "        }\n", "\n", "        throw new RouteNotFoundException(\"Route [{$name}] not defined.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FUrlGenerator.php&line=517", "ajax": false, "filename": "UrlGenerator.php", "line": "517"}}, {"type": "Symfony\\Component\\Routing\\Exception\\RouteNotFoundException", "message": "Route [marketplace.vendor.chatbase.training] not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php", "line": 517, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:80</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>853</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Routing\\UrlGenerator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">marketplace.vendor.chatbase.training</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/e7a33947db911c514182339d94e266f0.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">marketplace.vendor.chatbase.training</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">D:\\laragon\\www\\muhrak\\storage\\framework\\views\\e7a33947db911c514182339d94e266f0.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">D:\\laragon\\www\\muhrak\\storage\\framework\\views/e7a33947db911c514182339d94e266f0.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Shortcode\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Shortcode\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21462 title=\"3 occurrences\">#1462</a> &#8230;27}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24476 title=\"3 occurrences\">#4476</a><samp data-depth=5 id=sf-dump-**********-ref24476 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Customer\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24527 title=\"3 occurrences\">#4527</a><samp data-depth=5 id=sf-dump-**********-ref24527 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">ec_customers</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-26</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">activated</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>block_reason</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_vendor</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stripe_account_active</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-26</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 20:24:55</span>\"\n            \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">activated</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>block_reason</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_vendor</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n            \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stripe_account_active</span>\" => <span class=sf-dump-num>0</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Botble\\Ecommerce\\Enums\\CustomerStatusEnum</span>\"\n            \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            \"<span class=sf-dump-key>is_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22227 title=\"4 occurrences\">#2227</a><samp data-depth=7 id=sf-dump-**********-ref22227 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">mp_stores</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hironic</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.345</span>\"\n                \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>address</span>\" => \"\"\n                \"<span class=sf-dump-key>country</span>\" => \"\"\n                \"<span class=sf-dump-key>state</span>\" => \"\"\n                \"<span class=sf-dump-key>city</span>\" => \"\"\n                \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"17 characters\">ff78a236d1ffa.jpg</span>\"\n                \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">unnamed-9.jpg</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"126 characters\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n                \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n                \"<span class=sf-dump-key>company</span>\" => \"\"\n                \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n                \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hironic</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.345</span>\"\n                \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>address</span>\" => \"\"\n                \"<span class=sf-dump-key>country</span>\" => \"\"\n                \"<span class=sf-dump-key>state</span>\" => \"\"\n                \"<span class=sf-dump-key>city</span>\" => \"\"\n                \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n                \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"17 characters\">ff78a236d1ffa.jpg</span>\"\n                \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">unnamed-9.jpg</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"126 characters\">HIRONIC leads the aesthetic medical device industry by providing differentiated and high quality HIFU, RF and Laser equipment.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 21:27:47</span>\"\n                \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n                \"<span class=sf-dump-key>company</span>\" => \"\"\n                \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n                \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Botble\\Marketplace\\Enums\\StoreStatusEnum</span>\"\n                \"<span class=sf-dump-key>main_type</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Botble\\Ecommerce\\Enums\\MainTypeEnum</span>\"\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n                \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"7 characters\">country</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">state</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">logo</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">logo_square</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"11 characters\">cover_image</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">zip_code</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">certificate_file</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">government_id_file</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"6 characters\">tax_id</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"9 characters\">main_type</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"6 characters\">avatar</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">is_verified</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22227 title=\"4 occurrences\">#2227</a>}\n        \"<span class=sf-dump-key>agent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Chatbase\\Models\\ChatbaseAgent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Chatbase\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ChatbaseAgent</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24562 title=\"3 occurrences\">#4562</a><samp data-depth=5 id=sf-dump-**********-ref24562 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">chatbase_agents</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">This is agent for the testing</span>\"\n            \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>chatbot_id</span>\" => \"<span class=sf-dump-str title=\"21 characters\">VzsKZKoybj6Yoj-G744eN</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"125 characters\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:06:58</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Hironic Vendor</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">This is agent for the testing</span>\"\n            \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>410</span>\n            \"<span class=sf-dump-key>chatbot_id</span>\" => \"<span class=sf-dump-str title=\"21 characters\">VzsKZKoybj6Yoj-G744eN</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"125 characters\">{&quot;model&quot;:&quot;gpt-4o-mini&quot;,&quot;temperature&quot;:0,&quot;widget&quot;:{&quot;enabled&quot;:true,&quot;position&quot;:&quot;bottom-right&quot;,&quot;theme&quot;:&quot;light&quot;},&quot;instructions&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:06:58</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-27 19:07:02</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>settings</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>training_data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>last_trained_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>last_synced_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">chatbot_id</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">training_data</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"15 characters\">last_trained_at</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">last_synced_at</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>conversations</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Widget or Iframe</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ee3ab7c-8bf2-427e-9933-7023a14b476e</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e048ce11-1b15-46f3-b8e5-3afd2ad661f3</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"12 characters\">who are you?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f18b9473-c484-430d-8dcc-0b480870bfeb</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.012970387</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">3b7bba8f-3572-4ac0-9000-44b09ce48d15</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"175 characters\">I&#039;m an AI chatbot here to assist you with your inquiries, issues, and requests related to aesthetic medical devices, particularly those from Hironic. How can I help you today?</span>\"\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ad81183b-8942-47b7-905e-9f05dd2f64a5</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"30 characters\">how many products do you have?</span>\"\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d9dc994d-f445-4148-9898-5d3fe77a055e</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.00012731104</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4977822c-5b13-48d4-9e54-7528e06f6bf1</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"258 characters\">I can provide information about Hironic&#039;s products, such as the DOUBLO-Gold, which is a beauty equipment device known for its non-invasive facelift and skin rejuvenation features. If you have specific questions about this product or others, feel free to ask!</span>\"\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bfee1c6b-ec3d-4af5-8372-0508e748858c</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ok thanks</span>\"\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">048f2b8b-6f17-4622-908f-53b55d7e73c2</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">95c4ea8e-0c20-4d14-b618-d18e25758325</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"116 characters\">You&#039;re welcome! If you have any more questions or need further assistance, feel free to reach out. Have a great day!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:15.167025+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:53.138871+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Playground</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">33dca671-0a64-4d59-a086-5d4025624554</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ee4e3af5-273a-4e28-af4c-6fcfcc3b0da8</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"25 characters\">hi, what is your purpose?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df297566-8325-493b-987c-c789c28b0718</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.002800927</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6bffb11b-b27d-4026-8472-cf66a2eca655</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"231 characters\">Hello! My purpose is to assist you with any inquiries, issues, or requests you might have, especially related to the Hironic Store and its offerings. If you have any questions or need assistance, feel free to ask. I&#039;m here to help!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>0.002800927</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>collectedCustomers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>data</span>\" => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">D:\\laragon\\www\\muhrak\\storage\\framework\\views/e7a33947db911c514182339d94e266f0.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Shortcode\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Shortcode\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21462 title=\"3 occurrences\">#1462</a> &#8230;27}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24476 title=\"3 occurrences\">#4476</a>}\n        \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Customer\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24527 title=\"3 occurrences\">#4527</a>}\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22227 title=\"4 occurrences\">#2227</a>}\n        \"<span class=sf-dump-key>agent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Chatbase\\Models\\ChatbaseAgent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Chatbase\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ChatbaseAgent</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24562 title=\"3 occurrences\">#4562</a>}\n        \"<span class=sf-dump-key>conversations</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Widget or Iframe</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ee3ab7c-8bf2-427e-9933-7023a14b476e</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e048ce11-1b15-46f3-b8e5-3afd2ad661f3</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"12 characters\">who are you?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f18b9473-c484-430d-8dcc-0b480870bfeb</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.012970387</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">3b7bba8f-3572-4ac0-9000-44b09ce48d15</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"175 characters\">I&#039;m an AI chatbot here to assist you with your inquiries, issues, and requests related to aesthetic medical devices, particularly those from Hironic. How can I help you today?</span>\"\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ad81183b-8942-47b7-905e-9f05dd2f64a5</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"30 characters\">how many products do you have?</span>\"\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d9dc994d-f445-4148-9898-5d3fe77a055e</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.00012731104</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4977822c-5b13-48d4-9e54-7528e06f6bf1</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"258 characters\">I can provide information about Hironic&#039;s products, such as the DOUBLO-Gold, which is a beauty equipment device known for its non-invasive facelift and skin rejuvenation features. If you have specific questions about this product or others, feel free to ask!</span>\"\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bfee1c6b-ec3d-4af5-8372-0508e748858c</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ok thanks</span>\"\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">048f2b8b-6f17-4622-908f-53b55d7e73c2</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">95c4ea8e-0c20-4d14-b618-d18e25758325</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"116 characters\">You&#039;re welcome! If you have any more questions or need further assistance, feel free to reach out. Have a great day!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:15.167025+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:53.138871+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Playground</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">33dca671-0a64-4d59-a086-5d4025624554</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ee4e3af5-273a-4e28-af4c-6fcfcc3b0da8</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"25 characters\">hi, what is your purpose?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df297566-8325-493b-987c-c789c28b0718</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.002800927</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6bffb11b-b27d-4026-8472-cf66a2eca655</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"231 characters\">Hello! My purpose is to assist you with any inquiries, issues, or requests you might have, especially related to the Hironic Store and its offerings. If you have any questions or need assistance, feel free to ask. I&#039;m here to help!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>0.002800927</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>collectedCustomers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>data</span>\" => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Shortcode\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Shortcode\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21462 title=\"3 occurrences\">#1462</a> &#8230;27}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24476 title=\"3 occurrences\">#4476</a>}\n        \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Models\\Customer\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Customer</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24527 title=\"3 occurrences\">#4527</a>}\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22227 title=\"4 occurrences\">#2227</a>}\n        \"<span class=sf-dump-key>agent</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Chatbase\\Models\\ChatbaseAgent\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Chatbase\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ChatbaseAgent</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24562 title=\"3 occurrences\">#4562</a>}\n        \"<span class=sf-dump-key>conversations</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Widget or Iframe</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">2ee3ab7c-8bf2-427e-9933-7023a14b476e</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">e048ce11-1b15-46f3-b8e5-3afd2ad661f3</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"12 characters\">who are you?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">f18b9473-c484-430d-8dcc-0b480870bfeb</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.012970387</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">3b7bba8f-3572-4ac0-9000-44b09ce48d15</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"175 characters\">I&#039;m an AI chatbot here to assist you with your inquiries, issues, and requests related to aesthetic medical devices, particularly those from Hironic. How can I help you today?</span>\"\n                </samp>]\n                <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ad81183b-8942-47b7-905e-9f05dd2f64a5</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"30 characters\">how many products do you have?</span>\"\n                </samp>]\n                <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d9dc994d-f445-4148-9898-5d3fe77a055e</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.00012731104</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">4977822c-5b13-48d4-9e54-7528e06f6bf1</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"258 characters\">I can provide information about Hironic&#039;s products, such as the DOUBLO-Gold, which is a beauty equipment device known for its non-invasive facelift and skin rejuvenation features. If you have specific questions about this product or others, feel free to ask!</span>\"\n                </samp>]\n                <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">bfee1c6b-ec3d-4af5-8372-0508e748858c</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ok thanks</span>\"\n                </samp>]\n                <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">048f2b8b-6f17-4622-908f-53b55d7e73c2</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">95c4ea8e-0c20-4d14-b618-d18e25758325</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"116 characters\">You&#039;re welcome! If you have any more questions or need further assistance, feel free to reach out. Have a great day!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>9.6856085E-5</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:15.167025+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T20:47:53.138871+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Playground</span>\"\n              \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">33dca671-0a64-4d59-a086-5d4025624554</span>\"\n              \"<span class=sf-dump-key>form_submission</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hi! What can I help you with?</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ee4e3af5-273a-4e28-af4c-6fcfcc3b0da8</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"25 characters\">hi, what is your purpose?</span>\"\n                </samp>]\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">df297566-8325-493b-987c-c789c28b0718</span>\"\n                  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"9 characters\">assistant</span>\"\n                  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                  \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>0.002800927</span>\n                  \"<span class=sf-dump-key>source</span>\" => \"<span class=sf-dump-str title=\"3 characters\">llm</span>\"\n                  \"<span class=sf-dump-key>stepId</span>\" => \"<span class=sf-dump-str title=\"36 characters\">6bffb11b-b27d-4026-8472-cf66a2eca655</span>\"\n                  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"231 characters\">Hello! My purpose is to assist you with any inquiries, issues, or requests you might have, especially related to the Hironic Store and its offerings. If you have any questions or need assistance, feel free to ask. I&#039;m here to help!</span>\"\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>min_score</span>\" => <span class=sf-dump-num>0.002800927</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>last_message_at</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2025-06-27T19:10:12.409685+00:00</span>\"\n              \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Pakistan</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>leads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>collectedCustomers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>data</span>\" => []\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"45 characters\">platform/packages/shortcode/src/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Botble\\Shortcode\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"35 characters\">[object Botble\\Shortcode\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">platform/plugins/marketplace/src/Http/Middleware/RedirectIfNotVendor.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Botble\\Marketplace\\Http\\Middleware\\RedirectIfNotVendor</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Botble\\Base\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"74 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">platform/core/base/src/Http/Middleware/HttpsProtocolMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">platform/core/base/src/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Botble\\Base\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">platform/packages/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Botble\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $url;\n", "        }\n", "\n", "        throw new RouteNotFoundException(\"Route [{$name}] not defined.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FUrlGenerator.php&line=517", "ajax": false, "filename": "UrlGenerator.php", "line": "517"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.83911, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.219326, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.223704, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.22641, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.228966, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.249748, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.252776, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.255371, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}, {"name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "param_count": null, "params": [], "start": **********.257844, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.phpplugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fresources%2Fviews%2Fthemes%2Fvendor-dashboard%2Fchatbase%2Fanalytics.blade.php&line=1", "ajax": false, "filename": "analytics.blade.php", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.005150000000000001, "accumulated_duration_str": "5.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.994785, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 9.126}, {"sql": "select * from `ec_customers` where `id` = 410 limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "vendor", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Middleware\\RedirectIfNotVendor.php", "line": 14}], "start": **********.998769, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.126, "width_percent": 5.825}, {"sql": "select * from `mp_stores` where `mp_stores`.`customer_id` = 410 and `mp_stores`.`customer_id` is not null limit 1", "type": "query", "params": [], "bindings": [410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 142}, {"index": 24, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\AnalyticsController.php", "line": 18}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.00741, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.951, "width_percent": 9.515}, {"sql": "select * from `chatbase_agents` where `id` = '1' and `store_id` = 410 limit 1", "type": "query", "params": [], "bindings": ["1", 410], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Http\\Controllers\\Fronts\\AnalyticsController.php", "line": 26}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.010485, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 24.466, "width_percent": 6.99}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.847526, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 31.456, "width_percent": 17.282}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.220265, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 48.738, "width_percent": 8.35}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.224518, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 57.087, "width_percent": 6.796}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.227165, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 63.883, "width_percent": 5.825}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.229799, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 69.709, "width_percent": 5.825}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2506518, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 75.534, "width_percent": 7.961}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.253573, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 83.495, "width_percent": 6.019}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2561362, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 89.515, "width_percent": 5.243}, {"sql": "select sum(`character_count`) as aggregate from `chatbase_training_sources` where `chatbase_training_sources`.`agent_id` = 1 and `chatbase_training_sources`.`agent_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, {"index": 20, "namespace": "view", "name": "plugins/chatbase::themes.vendor-dashboard.chatbase.analytics", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.258593, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChatbaseAgent.php:81", "source": {"index": 19, "namespace": null, "name": "platform/plugins/chatbase/src/Models/ChatbaseAgent.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\src\\Models\\ChatbaseAgent.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=81", "ajax": false, "filename": "ChatbaseAgent.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 94.757, "width_percent": 5.243}]}, "models": {"data": {"Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Chatbase\\Models\\ChatbaseAgent": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FModels%2FChatbaseAgent.php&line=1", "ajax": false, "filename": "ChatbaseAgent.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://muhrak.gc/vendor/chatbase/1/analytics", "action_name": "marketplace.vendor.chatbase.analytics", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index", "uri": "GET vendor/chatbase/{id}/analytics", "controller": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FAnalyticsController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Chatbase\\Http\\Controllers\\Fronts", "prefix": "vendor/chatbase", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fsrc%2FHttp%2FControllers%2FFronts%2FAnalyticsController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/chatbase/src/Http/Controllers/Fronts/AnalyticsController.php:13-49</a>", "middleware": "web, core, vendor", "duration": "4.23s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1127648858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1127648858\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-839964899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-839964899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-818014405 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://muhrak.gc/vendor/chatbase</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3195 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlDTjEzRXB6SlpmaEpNdU05a0dYclE9PSIsInZhbHVlIjoiQUJTdmJNQ21CbDQ1Ump2ZmsxUDVsZHJwVk1IZVNWeVBHRDVGSHVud3Zudy9xZ0J1dFZVeTFUZGlKNExuUmwrNDVDaFU3V1pZTnpXNTJFZm1TZVExeGFkWEE5eFh2ZlBjUzdHNnUydm9tTEpRaHVWS21JaWRPbEoyUlpZdGhLRjNBRUl6b1NuWWJ6cjVROTN0TUZCZ09sa2lRNlM5c2ZnYUVvUXUrTG9YOTc2UFFPMW1McmRVN09ELzVMQytXN3pMUGJjSURJbncxVjRKNkNXbUtpWW81VUhiTnNmVk5kLy92bmZJUm9nbzNCMD0iLCJtYWMiOiIwNDExNTA2N2Y2MDJiZTc2NjMxYjMwNjE1YjllMjBkMDJjMmU0M2Q2OTc1NWQ3Y2Y5YjBhOWEzMjE5ZWJiYmYxIiwidGFnIjoiIn0%3D; chatbase_anon_id=46b86d34-f4ab-44b2-98e8-d732019a3e3d; XSRF-TOKEN=eyJpdiI6IjJ1SWZkZXpsdmpJK0NUSE43RG9kUEE9PSIsInZhbHVlIjoiZmlkczE3TlRudGoza0RmL3pndjZtYmRXWWl6WVRGek1VRTg0bk1BTlExL0s2N3F4dzBaZktKY3MwNkMxRDlWVjZyNkpMdWttTTQyb3JsOE5QSjB6dFZFS1RLa1dpOE1XNWRMTEhLLzQxOXl3WTZlSThNNU1lb0tuQlhxVzNCUEQiLCJtYWMiOiI2MDllNDdmMjFlZWNlZmRhMGExYmEzYTNkN2Y2NjQ4MjQxZDgzOWI5MTUwNmViZDllYWM1YTBiYzFlZGZiZGQ5IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlNXbUxKQ0ZVSGU5SkFKYVVmR2NPaHc9PSIsInZhbHVlIjoiLzlOc2RtWUlaNDkzQzZzWWxNaG43Q0tnMGtDbXZRUjFNQ051L2dOVGtHSVlMaXY2enVtRXVIcVNTYzJMK1JCNlMzTk93c0tWS3ArbTc4cGVQODNkN3M4WGpiYlN4anplS1M2Smg5K1hXNndGeGVJYUFCQ2hmcGlXTFhudDBFbDgiLCJtYWMiOiI1ZWZkN2FjYmVkN2UzYjM2NWIzODRmYmNiYmE1Y2M2Zjk3MTI2ZGU0YzQxMDA2YWI5ZjExYjM4Y2Q2MWE2NDNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818014405\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"125 characters\">410|re29qUAbtWl1IeKFhcYwSUOMx5xVLBnvI1egFP3la2cxz0KpetWmyZMAMnhc|$2y$12$FQ50j/6Ixi48MRWxJzy1mO4gMqPc./48zhHMyx12TV2y0102AtOr6</span>\"\n  \"<span class=sf-dump-key>chatbase_anon_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yrLyALnx9eBThTSh0iG06HRtjQ1Ex23uOlcwUDZh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-376324706 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 20:53:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376324706\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1203045361 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">s7FxZf4LYwGr3nokArFHqh0NqBw3fANb2aHl78bz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://muhrak.gc/vendor/chatbase</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>410</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>10821</span> => <span class=sf-dump-num>1751051544</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203045361\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://muhrak.gc/vendor/chatbase/1/analytics", "action_name": "marketplace.vendor.chatbase.analytics", "controller_action": "Botble\\Chatbase\\Http\\Controllers\\Fronts\\AnalyticsController@index"}, "badge": "500 Internal Server Error"}}