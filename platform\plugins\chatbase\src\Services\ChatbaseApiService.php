<?php

namespace Botble\Chatbase\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ChatbaseApiService
{
    protected string $apiKey;
    protected string $apiUrl;

    public function __construct()
    {
        $this->apiKey = config('plugins.chatbase.general.api_key');
        $this->apiUrl = config('plugins.chatbase.general.api_url');
    }

    public function createChatbot(string $name, string $sourceText = ''): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/create-chatbot', [
                'chatbotName' => $name,
                'sourceText' => $sourceText,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to create chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Create Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function updateChatbot(string $chatbotId, array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->put($this->apiUrl . '/update-chatbot', array_merge([
                'chatbotId' => $chatbotId,
            ], $data));

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function deleteChatbot(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->delete($this->apiUrl . '/delete-chatbot', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to delete chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Delete Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getChatbots(): array
    {
        // dd($this->apiKey, $this->apiUrl);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-chatbots');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get chatbots',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Chatbots: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getConversations(string $chatbotId, int $limit = 50): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-conversations', [
                'chatbotId' => $chatbotId,
                'limit' => $limit,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get conversations',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Conversations: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getLeads(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-leads', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get leads',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Leads: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function updateChatbotSettings(string $chatbotId, array $settings): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/update-chatbot-settings', array_merge([
                'chatbotId' => $chatbotId,
            ], $settings));

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot settings',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot Settings: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function testConnection(): array
    {
        return $this->getChatbots();
    }

    /**
     * Update chatbot training data on Chatbase
     */
    public function updateChatbotData(string $chatbotId, string $chatbotName, string $sourceText): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/update-chatbot-data', [
                'chatbotId' => $chatbotId,
                'chatbotName' => $chatbotName,
                'sourceText' => $sourceText,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot data',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot Data: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Upload chatbot icon
     */
    public function uploadChatbotIcon(string $chatbotId, $iconFile): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->attach('icon', $iconFile, 'icon.png')
            ->post($this->apiUrl . '/upload-chatbot-icon', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to upload chatbot icon',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Upload Chatbot Icon: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Delete chatbot icon
     */
    public function deleteChatbotIcon(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->delete($this->apiUrl . '/delete-chatbot-icon', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to delete chatbot icon',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Delete Chatbot Icon: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Upload chatbot profile picture
     */
    public function uploadChatbotProfilePicture(string $chatbotId, $profilePictureFile): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->attach('profilePicture', $profilePictureFile, 'profile.png')
            ->post($this->apiUrl . '/upload-chatbot-profile-picture', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to upload chatbot profile picture',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Upload Chatbot Profile Picture: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Delete chatbot profile picture
     */
    public function deleteChatbotProfilePicture(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->delete($this->apiUrl . '/delete-chatbot-profile-picture', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to delete chatbot profile picture',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Delete Chatbot Profile Picture: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Send message to chatbot
     */
    public function messageChatbot(string $chatbotId, string $message, ?string $conversationId = null): array
    {
        try {
            $payload = [
                'chatbotId' => $chatbotId,
                'message' => $message,
            ];

            if ($conversationId) {
                $payload['conversationId'] = $conversationId;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/message-chatbot', $payload);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to send message to chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Message Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create webhook for chatbot events
     */
    public function createWebhook(string $chatbotId, string $url, array $events = []): array
    {
        try {
            $payload = [
                'chatbotId' => $chatbotId,
                'url' => $url,
                'events' => $events ?: ['message', 'conversation_start', 'conversation_end'],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/create-webhook', $payload);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to create webhook',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Create Webhook: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook(string $webhookId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->delete($this->apiUrl . '/delete-webhook', [
                'webhookId' => $webhookId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to delete webhook',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Delete Webhook: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get webhook details
     */
    public function getWebhooks(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-webhooks', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get webhooks',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Webhooks: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }
}
