<?php

namespace Botble\Chatbase\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ChatbaseApiService
{
    protected string $apiKey;
    protected string $apiUrl;

    public function __construct()
    {
        $this->apiKey = config('plugins.chatbase.general.api_key');
        $this->apiUrl = config('plugins.chatbase.general.api_url');
    }

    public function createChatbot(string $name, string $sourceText = ''): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/create-chatbot', [
                'chatbotName' => $name,
                'sourceText' => $sourceText,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to create chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Create Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function updateChatbot(string $chatbotId, array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->put($this->apiUrl . '/update-chatbot', array_merge([
                'chatbotId' => $chatbotId,
            ], $data));

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function deleteChatbot(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->delete($this->apiUrl . '/delete-chatbot', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to delete chatbot',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Delete Chatbot: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getChatbots(): array
    {
        // dd($this->apiKey, $this->apiUrl);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-chatbots');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get chatbots',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Chatbots: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getConversations(string $chatbotId, int $limit = 50): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-conversations', [
                'chatbotId' => $chatbotId,
                'limit' => $limit,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get conversations',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Conversations: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function getLeads(string $chatbotId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
            ])->get($this->apiUrl . '/get-leads', [
                'chatbotId' => $chatbotId,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to get leads',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Get Leads: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function updateChatbotSettings(string $chatbotId, array $settings): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/update-chatbot-settings', array_merge([
                'chatbotId' => $chatbotId,
            ], $settings));

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot settings',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot Settings: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }

    public function testConnection(): array
    {
        return $this->getChatbots();
    }

    /**
     * Update chatbot training data on Chatbase
     */
    public function updateChatbotData(string $chatbotId, string $chatbotName, string $sourceText): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/update-chatbot-data', [
                'chatbotId' => $chatbotId,
                'chatbotName' => $chatbotName,
                'sourceText' => $sourceText,
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['message'] ?? 'Failed to update chatbot data',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Chatbase API Error - Update Chatbot Data: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'API connection failed: ' . $e->getMessage(),
            ];
        }
    }
}
