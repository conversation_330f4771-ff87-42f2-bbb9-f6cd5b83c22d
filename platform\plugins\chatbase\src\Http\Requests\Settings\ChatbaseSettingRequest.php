<?php

namespace Botble\Chatbase\Http\Requests\Settings;

use Bo<PERSON>ble\Support\Http\Requests\Request;

class ChatbaseSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            'chatbase_api_key' => ['nullable', 'string', 'max:255'],
            'chatbase_widget_enabled' => ['nullable', 'boolean'],
            'chatbase_auto_embed_widget' => ['nullable', 'boolean'],
            'chatbase_default_model' => ['nullable', 'string', 'in:gpt-4o-mini,gpt-4o,gpt-4-turbo,gpt-4,claude-3-5-sonnet,claude-3-opus,claude-3-haiku,gemini-1.5-pro,gemini-1.5-flash'],
            'chatbase_widget_position' => ['nullable', 'string', 'in:bottom-right,bottom-left,top-right,top-left'],
            'chatbase_widget_theme' => ['nullable', 'string', 'in:light,dark,auto'],
            'chatbase_default_instructions' => ['nullable', 'string', 'max:2000'],
        ];
    }

    public function attributes(): array
    {
        return [
            'chatbase_api_key' => trans('plugins/chatbase::chatbase.settings.api_key'),
            'chatbase_widget_enabled' => trans('plugins/chatbase::chatbase.settings.widget_enabled'),
            'chatbase_auto_embed_widget' => trans('plugins/chatbase::chatbase.settings.auto_embed_widget'),
            'chatbase_default_model' => trans('plugins/chatbase::chatbase.settings.default_model'),
            'chatbase_widget_position' => trans('plugins/chatbase::chatbase.settings.widget_position'),
            'chatbase_widget_theme' => trans('plugins/chatbase::chatbase.settings.widget_theme'),
            'chatbase_default_instructions' => trans('plugins/chatbase::chatbase.settings.default_instructions'),
        ];
    }
}
