<?php

namespace Bo<PERSON>ble\Chatbase\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\FormAbstract;
use Botble\Chatbase\Http\Requests\ChatbaseAgentRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Marketplace\Facades\MarketplaceHelper;

class ChatbaseAgentForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->template(MarketplaceHelper::viewPath('vendor-dashboard.forms.base'))
            ->setValidatorClass(ChatbaseAgentRequest::class)
            ->add(
                'name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.name'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.name_helper'))
                    ->required()
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.name_placeholder'),
                        'data-counter' => 255,
                    ])
            )
            ->add(
                'description',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.description'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.description_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.description_placeholder'),
                    ])
            )
            ->add(
                'training_text',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.training_text'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.training_text_helper'))
                    ->rows(6)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.training_text_placeholder'),
                    ])
            );
    }
}
