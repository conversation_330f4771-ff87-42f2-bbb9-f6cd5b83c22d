# Chatbase Integration Plugin

This plugin integrates Chatbase.co AI agents into your marketplace, allowing vendors to create and manage their own AI-powered customer support agents.

## Features

### Admin Features
- **API Key Management**: Secure configuration of Chatbase API credentials
- **Global Settings**: Configure default models, widget appearance, and behavior
- **Auto-embed Control**: Enable/disable automatic widget embedding on store pages

### Vendor Features
- **Agent Creation**: Easy-to-use interface for creating Chatbase agents
- **Agent Management**: Edit agent settings, model selection, and behavior
- **Training Interface**: Add and manage training content (text, URLs)
- **Widget Customization**: Control widget position, theme, and visibility
- **Analytics Access**: Direct links to Chatbase analytics dashboard

### Technical Features
- **Automatic Widget Embedding**: Seamlessly integrates chat widgets on store pages
- **Dynamic Agent Assignment**: Each store gets its own unique agent
- **Training Data Management**: Support for multiple content sources
- **Error Handling**: Comprehensive error reporting and logging
- **API Integration**: Full integration with Chatbase REST API

## Installation

1. Place the plugin in `platform/plugins/chatbase/`
2. Run migrations: `php artisan migrate`
3. Activate the plugin in the admin panel
4. Configure your Chatbase API key in Settings > Chatbase

## Configuration

### Admin Settings
Navigate to **Settings > Chatbase** to configure:
- Chatbase API Key
- Default AI Model
- Widget Position and Theme
- Auto-embed Settings

### Vendor Usage
Vendors can access Chatbase features through their dashboard:
1. Go to **Vendor Dashboard > Chatbase Agent**
2. Click **Create Agent** to set up their AI assistant
3. Customize agent settings and add training content
4. The widget will automatically appear on their store page

## API Integration

The plugin uses the following Chatbase API endpoints:
- `POST /api/v1/create-chatbot` - Create new agents
- `PUT /api/v1/update-chatbot` - Update agent settings
- `DELETE /api/v1/delete-chatbot` - Remove agents
- `GET /api/v1/get-chatbots` - List all agents

## Database Schema

### chatbase_agents
- Stores agent information and settings
- Links agents to stores and customers
- Tracks status and sync information

### chatbase_training_sources
- Manages training content for each agent
- Supports text, URL, and file sources
- Tracks character counts for billing

## Requirements

- Chatbase.co account with API access
- Active marketplace with vendor functionality
- PHP 8.1+ with cURL extension

## Support

For issues and feature requests, please contact the development team.
