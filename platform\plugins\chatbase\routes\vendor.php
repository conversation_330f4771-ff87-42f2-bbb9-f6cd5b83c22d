<?php

use <PERSON><PERSON><PERSON>\Chatbase\Http\Controllers\Fronts\AnalyticsController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Fronts\ChatbaseController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Fronts\ChatbotController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Fronts\ChatbotConversationsController;
use Botble\Chatbase\Http\Controllers\Fronts\ChatbotMediaController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Fronts\ChatbotSettingsController;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace' => 'Botble\Chatbase\Http\Controllers\Fronts',
    'prefix' => config('plugins.marketplace.general.vendor_panel_dir', 'vendor'),
    'as' => 'marketplace.vendor.',
    'middleware' => ['web', 'core', 'vendor'],
], function (): void {
    // Legacy chatbase routes (for backward compatibility)
    Route::group(['prefix' => 'chatbase', 'as' => 'chatbase.'], function (): void {
        Route::get('/', [ChatbaseController::class, 'index'])->name('index');
        Route::get('create', [ChatbaseController::class, 'create'])->name('create');
        Route::post('/', [ChatbaseController::class, 'store'])->name('store');
        Route::get('{id}/edit', [ChatbaseController::class, 'edit'])->name('edit');
        Route::put('{id}', [ChatbaseController::class, 'update'])->name('update');
        Route::get('{id}/settings', [ChatbaseController::class, 'settings'])->name('settings');
        Route::post('{id}/settings', [ChatbaseController::class, 'updateSettings'])->name('update-settings');
        Route::delete('{id}', [ChatbaseController::class, 'destroy'])->name('destroy');
        Route::get('{id}/analytics', [AnalyticsController::class, 'index'])->name('analytics');
    });

    // New structured chatbot routes
    Route::group(['prefix' => 'chatbot', 'as' => 'chatbot.'], function (): void {
        // Main chatbot management
        Route::get('/', [ChatbotController::class, 'index'])->name('index');
        Route::get('create', [ChatbotController::class, 'create'])->name('create');
        Route::post('/', [ChatbotController::class, 'store'])->name('store');
        Route::get('{id}/edit', [ChatbotController::class, 'edit'])->name('edit');
        Route::put('{id}', [ChatbotController::class, 'update'])->name('update');
        Route::delete('{id}', [ChatbotController::class, 'destroy'])->name('destroy');

        // Chatbot settings (vendor-limited)
        Route::group(['prefix' => '{id}/settings', 'as' => 'settings.'], function (): void {
            Route::get('/', [ChatbotSettingsController::class, 'edit'])->name('edit');
            Route::put('/', [ChatbotSettingsController::class, 'update'])->name('update');
        });

        // Media management
        Route::group(['prefix' => '{id}/media', 'as' => 'media.'], function (): void {
            Route::get('/', [ChatbotMediaController::class, 'index'])->name('index');
            Route::post('icon', [ChatbotMediaController::class, 'uploadIcon'])->name('icon.upload');
            Route::delete('icon', [ChatbotMediaController::class, 'deleteIcon'])->name('icon.delete');
            Route::post('profile-picture', [ChatbotMediaController::class, 'uploadProfilePicture'])->name('profile-picture.upload');
            Route::delete('profile-picture', [ChatbotMediaController::class, 'deleteProfilePicture'])->name('profile-picture.delete');
        });

        // Conversations and Leads
        Route::group(['prefix' => '{id}/conversations', 'as' => 'conversations.'], function (): void {
            Route::get('/', [ChatbotConversationsController::class, 'index'])->name('index');
            Route::post('send-message', [ChatbotConversationsController::class, 'sendMessage'])->name('send-message');
        });

        Route::get('{id}/leads', [ChatbotConversationsController::class, 'leads'])->name('leads');

        // Analytics
        Route::get('{id}/analytics', [AnalyticsController::class, 'index'])->name('analytics');
    });
});
