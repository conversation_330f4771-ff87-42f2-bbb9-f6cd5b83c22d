<?php $__env->startSection('content'); ?>
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3><?php echo e(__('Agent Analytics')); ?> - <?php echo e($agent->name); ?></h3>
            <p class="text-muted"><?php echo e(__('View your agent performance and customer interactions')); ?></p>
        </div>
        <div class="ps-section__content">
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo e(count($conversations)); ?></h5>
                            <p class="card-text"><?php echo e(__('Total Conversations')); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo e(count($leads)); ?></h5>
                            <p class="card-text"><?php echo e(__('Leads Generated')); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?php echo e(number_format($agent->getTotalCharacterCount())); ?></h5>
                            <p class="card-text"><?php echo e(__('Training Characters')); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">
                                <?php if($agent->last_trained_at): ?>
                                    <?php echo e($agent->last_trained_at->diffForHumans()); ?>

                                <?php else: ?>
                                    <?php echo e(__('Never')); ?>

                                <?php endif; ?>
                            </h5>
                            <p class="card-text"><?php echo e(__('Last Trained')); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Recent Conversations -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><?php echo e(__('Recent Conversations')); ?></h5>
                            <?php if($agent->chatbot_id): ?>
                                <a href="https://www.chatbase.co/chatbot/<?php echo e($agent->chatbot_id); ?>/analytics"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fa fa-external-link"></i>
                                    <?php echo e(__('View Full Analytics')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <?php if(count($conversations) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th><?php echo e(__('Date')); ?></th>
                                                <th><?php echo e(__('Messages')); ?></th>
                                                <th><?php echo e(__('Duration')); ?></th>
                                                <th><?php echo e(__('Status')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = array_slice($conversations, 0, 10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $conversation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <?php if(isset($conversation['createdAt'])): ?>
                                                            <?php echo e(\Carbon\Carbon::parse($conversation['createdAt'])->format('M j, Y H:i')); ?>

                                                        <?php else: ?>
                                                            <?php echo e(__('Unknown')); ?>

                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo e($conversation['messageCount'] ?? 0); ?></td>
                                                    <td>
                                                        <?php if(isset($conversation['duration'])): ?>
                                                            <?php echo e(gmdate('H:i:s', $conversation['duration'])); ?>

                                                        <?php else: ?>
                                                            <?php echo e(__('N/A')); ?>

                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo e(($conversation['status'] ?? 'active') === 'completed' ? 'success' : 'primary'); ?>">
                                                            <?php echo e(ucfirst($conversation['status'] ?? 'active')); ?>

                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fa fa-message-circle" style="font-size: 3rem; color: #ccc;"></i>
                                    <p class="text-muted mt-2"><?php echo e(__('No conversations yet')); ?></p>
                                    <p class="text-muted"><?php echo e(__('Conversations will appear here once customers start chatting with your agent')); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Leads -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('Recent Leads')); ?></h5>
                        </div>
                        <div class="card-body">
                            <?php if(count($leads) > 0): ?>
                                <?php $__currentLoopData = array_slice($leads, 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <strong><?php echo e($lead['name'] ?? __('Anonymous')); ?></strong>
                                            <small class="text-muted">
                                                <?php if(isset($lead['createdAt'])): ?>
                                                    <?php echo e(\Carbon\Carbon::parse($lead['createdAt'])->diffForHumans()); ?>

                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <?php if(isset($lead['email'])): ?>
                                            <div class="text-muted"><?php echo e($lead['email']); ?></div>
                                        <?php endif; ?>
                                        <?php if(isset($lead['phone'])): ?>
                                            <div class="text-muted"><?php echo e($lead['phone']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="text-center py-3">
                                    <i class="fa fa-users" style="font-size: 2rem; color: #ccc;"></i>
                                    <p class="text-muted mt-2"><?php echo e(__('No leads yet')); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('Quick Actions')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('marketplace.vendor.chatbase.edit', $agent->id)); ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fa fa-edit"></i>
                                    <?php echo e(__('Edit Agent')); ?>

                                </a>
                                <?php if($agent->chatbot_id): ?>
                                    <a href="https://www.chatbase.co/chatbot/<?php echo e($agent->chatbot_id); ?>"
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fa fa-external-link"></i>
                                        <?php echo e(__('Chatbase Dashboard')); ?>

                                    </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('marketplace.vendor.chatbase.index')); ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fa fa-arrow-left"></i>
                                    <?php echo e(__('Back to Agent')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer'); ?>
<script>
// Auto-refresh analytics every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000); // 5 minutes
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laragon\www\muhrak\platform/plugins/chatbase/resources/views/themes/vendor-dashboard/chatbase/analytics.blade.php ENDPATH**/ ?>