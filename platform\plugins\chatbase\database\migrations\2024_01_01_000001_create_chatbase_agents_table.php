<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('chatbase_agents', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('store_id')->constrained('mp_stores')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('chatbot_id')->nullable()->unique();
            $table->string('status')->default('draft'); // draft, creating, active, error
            $table->json('settings')->nullable();
            $table->json('training_data')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('last_trained_at')->nullable();
            $table->timestamp('last_synced_at')->nullable();
            $table->timestamps();

            $table->index(['store_id', 'status']);
            $table->index(['customer_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('chatbase_agents');
    }
};
