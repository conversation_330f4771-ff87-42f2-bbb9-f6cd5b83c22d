<?php

namespace Botble\Chatbase\Jobs;

use Bo<PERSON><PERSON>\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Chatbase\Services\ChatbaseApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateChatbaseAgentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ChatbaseAgent $agent;
    protected string $trainingText;

    public function __construct(ChatbaseAgent $agent, string $trainingText = '')
    {
        $this->agent = $agent;
        $this->trainingText = $trainingText;
    }

    public function handle(ChatbaseApiService $apiService): void
    {
        try {
            Log::info('Creating Chatbase agent via job', [
                'agent_id' => $this->agent->id,
                'agent_name' => $this->agent->name,
            ]);

            // Create chatbot via API
            $response = $apiService->createChatbot(
                $this->agent->name,
                $this->trainingText
            );

            if ($response['success']) {
                $this->agent->update([
                    'chatbot_id' => $response['data']['chatbotId'],
                    'status' => 'active',
                    'last_trained_at' => now(),
                    'last_synced_at' => now(),
                    'error_message' => null,
                ]);

                Log::info('Chatbase agent created successfully', [
                    'agent_id' => $this->agent->id,
                    'chatbot_id' => $response['data']['chatbotId'],
                ]);
            } else {
                $this->agent->update([
                    'status' => 'error',
                    'error_message' => $response['error'],
                ]);

                Log::error('Failed to create Chatbase agent', [
                    'agent_id' => $this->agent->id,
                    'error' => $response['error'],
                ]);
            }
        } catch (\Exception $e) {
            $this->agent->update([
                'status' => 'error',
                'error_message' => 'Job failed: ' . $e->getMessage(),
            ]);

            Log::error('Chatbase agent creation job failed', [
                'agent_id' => $this->agent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Chatbase agent creation job failed permanently', [
            'agent_id' => $this->agent->id,
            'error' => $exception->getMessage(),
        ]);

        $this->agent->update([
            'status' => 'error',
            'error_message' => 'Job failed permanently: ' . $exception->getMessage(),
        ]);
    }
}
