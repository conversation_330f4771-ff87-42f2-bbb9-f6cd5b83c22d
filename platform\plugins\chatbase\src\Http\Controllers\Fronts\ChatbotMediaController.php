<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Http\Requests\ChatbotMediaRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseApiService;
use Illuminate\Http\Request;

class ChatbotMediaController extends BaseController
{
    public function index(string $id, Request $request)
    {
        $this->pageTitle(__('Chatbot Media'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $data = compact('user', 'store', 'agent');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => view('plugins/chatbase::themes.vendor-dashboard.chatbot.media', $data)->render(),
                ]);
        }

        return view('plugins/chatbase::themes.vendor-dashboard.chatbot.media', $data);
    }

    public function uploadIcon(string $id, ChatbotMediaRequest $request, ChatbaseApiService $apiService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot must be created before uploading media'));
        }

        if (!$request->hasFile('icon')) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('No icon file provided'));
        }

        $iconFile = $request->file('icon');
        $result = $apiService->uploadChatbotIcon($agent->chatbot_id, $iconFile);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        // Update local record with icon URL if provided in response
        if (isset($result['data']['iconUrl'])) {
            $agent->update(['icon_url' => $result['data']['iconUrl']]);
        }

        return $this
            ->httpResponse()
            ->setMessage(__('Chatbot icon uploaded successfully!'));
    }

    public function deleteIcon(string $id, ChatbaseApiService $apiService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot must be created before managing media'));
        }

        $result = $apiService->deleteChatbotIcon($agent->chatbot_id);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        // Clear local icon URL
        $agent->update(['icon_url' => null]);

        return $this
            ->httpResponse()
            ->setMessage(__('Chatbot icon deleted successfully!'));
    }

    public function uploadProfilePicture(string $id, ChatbotMediaRequest $request, ChatbaseApiService $apiService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot must be created before uploading media'));
        }

        if (!$request->hasFile('profile_picture')) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('No profile picture file provided'));
        }

        $profilePictureFile = $request->file('profile_picture');
        $result = $apiService->uploadChatbotProfilePicture($agent->chatbot_id, $profilePictureFile);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        // Update local record with profile picture URL if provided in response
        if (isset($result['data']['profilePictureUrl'])) {
            $agent->update(['profile_picture_url' => $result['data']['profilePictureUrl']]);
        }

        return $this
            ->httpResponse()
            ->setMessage(__('Chatbot profile picture uploaded successfully!'));
    }

    public function deleteProfilePicture(string $id, ChatbaseApiService $apiService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->chatbot_id) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot must be created before managing media'));
        }

        $result = $apiService->deleteChatbotProfilePicture($agent->chatbot_id);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        // Clear local profile picture URL
        $agent->update(['profile_picture_url' => null]);

        return $this
            ->httpResponse()
            ->setMessage(__('Chatbot profile picture deleted successfully!'));
    }
}
