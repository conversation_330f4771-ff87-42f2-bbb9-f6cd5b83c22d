<?php

namespace Botble\Chatbase\Facades;

use Bo<PERSON>ble\Chatbase\Supports\ChatbaseHelper as ChatbaseHelperSupport;
use Illuminate\Support\Facades\Facade;

/**
 * @method static bool isConfigured()
 * @method static bool isWidgetEnabled()
 * @method static bool isAutoEmbedEnabled()
 * @method static string getDefaultModel()
 * @method static string getDefaultInstructions()
 * @method static \Botble\Chatbase\Models\ChatbaseAgent|null getAgentForStore(\Botble\Marketplace\Models\Store $store)
 * @method static \Botble\Chatbase\Models\ChatbaseAgent|null getActiveAgentForStore(\Botble\Marketplace\Models\Store $store)
 * @method static bool canCreateAgent(\Botble\Marketplace\Models\Store $store)
 * @method static string getWidgetScript(\Botble\Chatbase\Models\ChatbaseAgent $agent)
 * @method static array getAvailableModels()
 * @method static array getWidgetPositions()
 * @method static array getWidgetThemes()
 * @method static array getTemperatureOptions()
 * @method static string formatCharacterCount(int $count)
 * @method static string getStatusBadgeClass(string $status)
 * @method static string getStatusIcon(string $status)
 * @method static string getChatbaseUrl(string $path = '')
 * @method static string getAnalyticsUrl(string $chatbotId)
 * @method static string getDashboardUrl(string $chatbotId)
 *
 * @see \Botble\Chatbase\Supports\ChatbaseHelper
 */
class ChatbaseHelper extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ChatbaseHelperSupport::class;
    }
}
