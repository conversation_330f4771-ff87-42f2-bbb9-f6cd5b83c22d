@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ __('Leads') }}</h4>
                    <div class="card-header-action">
                        <a href="{{ route('marketplace.vendor.chatbot.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> {{ __('Back to Chatbot') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($error)
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            {{ __('Error loading leads') }}: {{ $error }}
                        </div>
                    @elseif(empty($leads))
                        <div class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5>{{ __('No Leads Yet') }}</h5>
                            <p class="text-muted">{{ __('Leads captured by your chatbot will appear here.') }}</p>
                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-lightbulb"></i> {{ __('How to capture leads:') }}</h6>
                                <ul class="mb-0 text-start">
                                    <li>{{ __('Configure your chatbot to ask for contact information') }}</li>
                                    <li>{{ __('Use lead capture forms in your chatbot conversations') }}</li>
                                    <li>{{ __('Set up automated lead qualification questions') }}</li>
                                </ul>
                            </div>
                        </div>
                    @else
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="alert alert-success">
                                    <i class="fas fa-chart-line"></i>
                                    {{ __('Total Leads Captured') }}: <strong>{{ count($leads) }}</strong>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Date') }}</th>
                                        <th>{{ __('Name') }}</th>
                                        <th>{{ __('Email') }}</th>
                                        <th>{{ __('Phone') }}</th>
                                        <th>{{ __('Source') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($leads as $lead)
                                        <tr>
                                            <td>
                                                @if(isset($lead['created_at']))
                                                    {{ \Carbon\Carbon::parse($lead['created_at'])->format('M j, Y H:i') }}
                                                @elseif(isset($lead['timestamp']))
                                                    {{ \Carbon\Carbon::parse($lead['timestamp'])->format('M j, Y H:i') }}
                                                @else
                                                    <span class="text-muted">{{ __('Unknown') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $lead['name'] ?? $lead['full_name'] ?? __('Unknown') }}</strong>
                                                @if(isset($lead['company']) && $lead['company'])
                                                    <br><small class="text-muted">{{ $lead['company'] }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if(isset($lead['email']) && $lead['email'])
                                                    <a href="mailto:{{ $lead['email'] }}">{{ $lead['email'] }}</a>
                                                @else
                                                    <span class="text-muted">{{ __('Not provided') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if(isset($lead['phone']) && $lead['phone'])
                                                    <a href="tel:{{ $lead['phone'] }}">{{ $lead['phone'] }}</a>
                                                @else
                                                    <span class="text-muted">{{ __('Not provided') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ $lead['source'] ?? __('Chatbot') }}
                                                </span>
                                            </td>
                                            <td>
                                                @if(isset($lead['status']))
                                                    @if($lead['status'] === 'new')
                                                        <span class="badge bg-primary">{{ __('New') }}</span>
                                                    @elseif($lead['status'] === 'contacted')
                                                        <span class="badge bg-warning">{{ __('Contacted') }}</span>
                                                    @elseif($lead['status'] === 'qualified')
                                                        <span class="badge bg-success">{{ __('Qualified') }}</span>
                                                    @elseif($lead['status'] === 'converted')
                                                        <span class="badge bg-success">{{ __('Converted') }}</span>
                                                    @else
                                                        <span class="badge bg-secondary">{{ $lead['status'] }}</span>
                                                    @endif
                                                @else
                                                    <span class="badge bg-primary">{{ __('New') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    @if(isset($lead['email']) && $lead['email'])
                                                        <a href="mailto:{{ $lead['email'] }}" class="btn btn-outline-primary" title="{{ __('Send Email') }}">
                                                            <i class="fas fa-envelope"></i>
                                                        </a>
                                                    @endif
                                                    @if(isset($lead['phone']) && $lead['phone'])
                                                        <a href="tel:{{ $lead['phone'] }}" class="btn btn-outline-success" title="{{ __('Call') }}">
                                                            <i class="fas fa-phone"></i>
                                                        </a>
                                                    @endif
                                                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#leadDetailsModal" data-lead="{{ json_encode($lead) }}" title="{{ __('View Details') }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        @if(count($leads) > 0)
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> {{ __('Lead Management Tips:') }}</h6>
                                        <ul class="mb-0">
                                            <li>{{ __('Follow up with leads within 24 hours for best results') }}</li>
                                            <li>{{ __('Use the contact information to reach out via email or phone') }}</li>
                                            <li>{{ __('Consider integrating with your CRM system for better lead tracking') }}</li>
                                            <li>{{ __('Review conversation history to understand lead context') }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Lead Details Modal -->
    <div class="modal fade" id="leadDetailsModal" tabindex="-1" aria-labelledby="leadDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="leadDetailsModalLabel">{{ __('Lead Details') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="leadDetailsContent">
                        <!-- Lead details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle lead details modal
    const leadDetailsModal = document.getElementById('leadDetailsModal');
    if (leadDetailsModal) {
        leadDetailsModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const leadData = JSON.parse(button.getAttribute('data-lead'));
            const contentDiv = leadDetailsModal.querySelector('#leadDetailsContent');
            
            let html = '<div class="row">';
            
            // Basic Information
            html += '<div class="col-md-6"><h6>{{ __("Basic Information") }}</h6>';
            html += '<table class="table table-sm">';
            html += '<tr><td><strong>{{ __("Name") }}:</strong></td><td>' + (leadData.name || leadData.full_name || '{{ __("Not provided") }}') + '</td></tr>';
            html += '<tr><td><strong>{{ __("Email") }}:</strong></td><td>' + (leadData.email || '{{ __("Not provided") }}') + '</td></tr>';
            html += '<tr><td><strong>{{ __("Phone") }}:</strong></td><td>' + (leadData.phone || '{{ __("Not provided") }}') + '</td></tr>';
            if (leadData.company) {
                html += '<tr><td><strong>{{ __("Company") }}:</strong></td><td>' + leadData.company + '</td></tr>';
            }
            html += '</table></div>';
            
            // Additional Information
            html += '<div class="col-md-6"><h6>{{ __("Additional Information") }}</h6>';
            html += '<table class="table table-sm">';
            html += '<tr><td><strong>{{ __("Source") }}:</strong></td><td>' + (leadData.source || '{{ __("Chatbot") }}') + '</td></tr>';
            html += '<tr><td><strong>{{ __("Status") }}:</strong></td><td>' + (leadData.status || '{{ __("New") }}') + '</td></tr>';
            if (leadData.created_at || leadData.timestamp) {
                const date = leadData.created_at || leadData.timestamp;
                html += '<tr><td><strong>{{ __("Date") }}:</strong></td><td>' + new Date(date).toLocaleString() + '</td></tr>';
            }
            html += '</table></div>';
            
            html += '</div>';
            
            // Message/Notes
            if (leadData.message || leadData.notes) {
                html += '<div class="row mt-3"><div class="col-12">';
                html += '<h6>{{ __("Message/Notes") }}</h6>';
                html += '<div class="bg-light p-3 rounded">' + (leadData.message || leadData.notes) + '</div>';
                html += '</div></div>';
            }
            
            // Conversation ID
            if (leadData.conversation_id) {
                html += '<div class="row mt-3"><div class="col-12">';
                html += '<h6>{{ __("Conversation") }}</h6>';
                html += '<p>{{ __("Conversation ID") }}: <code>' + leadData.conversation_id + '</code></p>';
                html += '</div></div>';
            }
            
            contentDiv.innerHTML = html;
        });
    }
});
</script>
@endpush
