<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\ChatbaseSettingsForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\ChatbaseSettingsRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseAgentService;

class ChatbotSettingsController extends BaseController
{
    public function edit(string $id)
    {
        $this->pageTitle(__('Chatbot Settings'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        return ChatbaseSettingsForm::createFromModel($agent)
            ->setUrl(route('marketplace.vendor.chatbot.settings.update', $agent->id))
            ->renderForm();
    }

    public function update(string $id, ChatbaseSettingsRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->updateAgentSettings($agent, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbot.index'))
            ->setMessage(__('Chatbot settings updated successfully!'));
    }
}
