<?php

namespace Bo<PERSON>ble\Chatbase\Forms\Admin;

use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\FieldOptions\TextareaFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\OnOffCheckboxField;
use Botble\Base\Forms\FormAbstract;
use Botble\Chatbase\Http\Requests\Admin\AdminChatbotRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Marketplace\Models\Store;

class AdminChatbotForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->setValidatorClass(AdminChatbotRequest::class)
            ->add(
                'store_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.admin.chatbot.store'))
                    ->helperText(trans('plugins/chatbase::chatbase.admin.chatbot.store_helper'))
                    ->required()
                    ->choices($this->getStoreChoices())
                    ->searchable()
            )
            ->add(
                'name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.name'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.name_helper'))
                    ->required()
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.name_placeholder'),
                        'data-counter' => 255,
                    ])
            )
            ->add(
                'description',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.description'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.description_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.description_placeholder'),
                    ])
            )
            ->add(
                'training_text',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.training_text'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.training_text_helper'))
                    ->rows(6)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.training_text_placeholder'),
                    ])
            )
            ->add(
                'instructions',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.instructions'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.instructions_helper'))
                    ->rows(4)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.instructions_placeholder'),
                    ])
            )
            ->add(
                'initial_messages',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.initial_messages'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.initial_messages_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.initial_messages_placeholder'),
                    ])
            )
            ->add(
                'suggested_messages',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.suggested_messages'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.suggested_messages_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.suggested_messages_placeholder'),
                    ])
            )
            ->add(
                'visibility',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.visibility'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.visibility_helper'))
                    ->choices([
                        'private' => trans('plugins/chatbase::chatbase.agent.visibility_private'),
                        'public' => trans('plugins/chatbase::chatbase.agent.visibility_public'),
                    ])
                    ->defaultValue('private')
            )
            ->add(
                'only_allow_on_added_domains',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.only_allow_on_added_domains'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.only_allow_on_added_domains_helper'))
                    ->defaultValue(false)
            )
            ->add(
                'domains',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.domains'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.domains_helper'))
                    ->rows(2)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.domains_placeholder'),
                    ])
            )
            ->add(
                'model',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.model'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.model_helper'))
                    ->choices([
                        'gpt-4o-mini' => 'GPT-4o Mini (Recommended)',
                        'gpt-4o' => 'GPT-4o',
                        'gpt-4-turbo' => 'GPT-4 Turbo',
                        'gpt-4' => 'GPT-4',
                        'claude-3-5-sonnet' => 'Claude 3.5 Sonnet',
                        'claude-3-opus' => 'Claude 3 Opus',
                        'claude-3-haiku' => 'Claude 3 Haiku',
                        'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                        'gemini-1.5-flash' => 'Gemini 1.5 Flash',
                    ])
                    ->defaultValue(setting('chatbase_default_model', 'gpt-4o-mini'))
            );
    }

    protected function getStoreChoices(): array
    {
        return Store::query()
            ->whereHas('customer')
            ->pluck('name', 'id')
            ->toArray();
    }
}
