<?php

namespace Botble\Chatbase\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatbaseTrainingSource extends BaseModel
{
    protected $table = 'chatbase_training_sources';

    protected $fillable = [
        'agent_id',
        'type',
        'title',
        'content',
        'url',
        'file_path',
        'status',
        'character_count',
    ];

    public function agent(): BelongsTo
    {
        return $this->belongsTo(ChatbaseAgent::class, 'agent_id');
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function updateCharacterCount(): void
    {
        $count = 0;
        
        if ($this->content) {
            $count = strlen($this->content);
        } elseif ($this->url) {
            // For URLs, we'll estimate based on typical webpage content
            $count = 5000; // Default estimate
        } elseif ($this->file_path) {
            // For files, we'll read and count if possible
            if (file_exists($this->file_path)) {
                $content = file_get_contents($this->file_path);
                $count = strlen($content);
            }
        }

        $this->update(['character_count' => $count]);
    }

    public function getDisplayContent(): string
    {
        if ($this->content) {
            return substr($this->content, 0, 200) . (strlen($this->content) > 200 ? '...' : '');
        }

        if ($this->url) {
            return $this->url;
        }

        if ($this->file_path) {
            return basename($this->file_path);
        }

        return '';
    }
}
