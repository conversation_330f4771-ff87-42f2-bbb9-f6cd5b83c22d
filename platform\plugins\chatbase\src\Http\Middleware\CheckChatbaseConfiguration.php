<?php

namespace Botble\Chatbase\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CheckChatbaseConfiguration
{
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = setting('chatbase_api_key');
        
        if (empty($apiKey)) {
            if ($request->ajax()) {
                return response()->json([
                    'error' => true,
                    'message' => __('plugins/chatbase::chatbase.messages.api_key_required'),
                ], 400);
            }

            return redirect()
                ->route('marketplace.vendor.chatbase.index')
                ->with('error_msg', __('plugins/chatbase::chatbase.messages.api_key_required'));
        }

        return $next($request);
    }
}
