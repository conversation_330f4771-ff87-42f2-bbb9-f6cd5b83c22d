<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\ChatbaseAgentForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\ChatbaseAgentRequest;
use Botble\Chatbase\Http\Requests\ChatbotUpdateRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseAgentService;
use Illuminate\Http\Request;

class ChatbotController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle(__('Chatbot Management'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('store_id', $store->id)->first();

        $data = compact('user', 'store', 'agent');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => view('plugins/chatbase::themes.vendor-dashboard.chatbot.index', $data)->render(),
                ]);
        }

        return view('plugins/chatbase::themes.vendor-dashboard.chatbot.index', $data);
    }

    public function create()
    {
        $this->pageTitle(__('Create Chatbot'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        // Check if agent already exists
        $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
        if ($existingAgent) {
            return redirect()
                ->route('marketplace.vendor.chatbot.index')
                ->with('error', __('Chatbot already exists for your store'));
        }

        return ChatbaseAgentForm::create()
            ->setUrl(route('marketplace.vendor.chatbot.store'))
            ->renderForm();
    }

    public function store(ChatbaseAgentRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        // Check if agent already exists
        $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
        if ($existingAgent) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage(__('Chatbot already exists for your store'));
        }

        $result = $agentService->createAgentForStore($store, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbot.index'))
            ->setMessage(__('Chatbot created successfully! You can now add training data and configure settings.'));
    }

    public function edit(string $id)
    {
        $this->pageTitle(__('Edit Chatbot'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        return ChatbaseAgentForm::createFromModel($agent)
            ->setUrl(route('marketplace.vendor.chatbot.update', $agent->id))
            ->renderForm();
    }

    public function update(string $id, ChatbotUpdateRequest $request, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->updateAgent($agent, $request->validated());

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbot.index'))
            ->setMessage(__('Chatbot updated successfully!'));
    }

    public function destroy(string $id, ChatbaseAgentService $agentService): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $result = $agentService->deleteAgent($agent);

        if (!$result['success']) {
            return $this
                ->httpResponse()
                ->setError()
                ->setMessage($result['error']);
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.vendor.chatbot.index'))
            ->setMessage(__('Chatbot deleted successfully!'));
    }
}
