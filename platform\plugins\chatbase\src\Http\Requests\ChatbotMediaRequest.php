<?php

namespace Botble\Chatbase\Http\Requests;

use Botble\Support\Http\Requests\Request;

class ChatbotMediaRequest extends Request
{
    public function rules(): array
    {
        return [
            'icon' => ['nullable', 'image', 'mimes:png,jpg,jpeg,gif', 'max:2048'],
            'profile_picture' => ['nullable', 'image', 'mimes:png,jpg,jpeg,gif', 'max:2048'],
        ];
    }

    public function attributes(): array
    {
        return [
            'icon' => trans('plugins/chatbase::chatbase.media.icon'),
            'profile_picture' => trans('plugins/chatbase::chatbase.media.profile_picture'),
        ];
    }
}
