<?php

namespace Bo<PERSON><PERSON>\Chatbase\Tables;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use <PERSON><PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;

class ChatbotTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->addActions([
                EditAction::make()->route('chatbase.admin.chatbots.edit'),
                DeleteAction::make()->route('chatbase.admin.chatbots.destroy'),
            ])
            ->removeAllBulkActions()
            ->removeAllBulkChanges();
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'description',
                'store_id',
                'customer_id',
                'chatbot_id',
                'status',
                'created_at',
                'last_trained_at',
                'last_synced_at',
            ]);

        // Only load relationships if they exist to avoid errors
        if (class_exists(\Botble\Marketplace\Models\Store::class)) {
            $query->with('store');
        }

        if (class_exists(\Botble\Ecommerce\Models\Customer::class)) {
            $query->with('customer');
        }

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make()->route('chatbase.admin.chatbots.edit'),
            Column::make('store_name')
                ->title(trans('plugins/chatbase::chatbase.admin.chatbot.store'))
                ->alignStart()
                ->renderUsing(function (ChatbaseAgent $item) {
                    if ($item->store) {
                        try {
                            return Html::link(
                                route('marketplace.store.edit', $item->store->id),
                                $item->store->name,
                                ['target' => '_blank']
                            );
                        } catch (\Exception $e) {
                            return $item->store->name ?? "Store ID: {$item->store_id}";
                        }
                    }
                    return $item->store_id ? "Store ID: {$item->store_id}" : '—';
                }),
            Column::make('customer_name')
                ->title(trans('plugins/chatbase::chatbase.admin.chatbot.customer'))
                ->alignStart()
                ->renderUsing(function (ChatbaseAgent $item) {
                    if ($item->customer) {
                        try {
                            return Html::link(
                                route('customers.edit', $item->customer->id),
                                $item->customer->name,
                                ['target' => '_blank']
                            );
                        } catch (\Exception $e) {
                            return $item->customer->name ?? "Customer ID: {$item->customer_id}";
                        }
                    }
                    return $item->customer_id ? "Customer ID: {$item->customer_id}" : '—';
                }),
            Column::make('chatbot_id')
                ->title(trans('plugins/chatbase::chatbase.admin.chatbot.chatbot_id'))
                ->alignStart()
                ->renderUsing(function (ChatbaseAgent $item) {
                    return $item->chatbot_id ? Html::tag('code', $item->chatbot_id) : '—';
                }),
            StatusColumn::make()
                ->renderUsing(function (ChatbaseAgent $item) {
                    $statusLabels = [
                        'active' => ['success', trans('plugins/chatbase::chatbase.status.active')],
                        'inactive' => ['secondary', trans('plugins/chatbase::chatbase.status.inactive')],
                        'creating' => ['warning', trans('plugins/chatbase::chatbase.status.creating')],
                        'error' => ['danger', trans('plugins/chatbase::chatbase.status.error')],
                    ];

                    $status = $statusLabels[$item->status] ?? ['secondary', $item->status];

                    return Html::tag('span', $status[1], [
                        'class' => 'badge bg-' . $status[0],
                    ]);
                }),
            Column::make('last_trained_at')
                ->title(trans('plugins/chatbase::chatbase.admin.chatbot.last_trained'))
                ->alignStart()
                ->renderUsing(function (ChatbaseAgent $item) {
                    return $item->last_trained_at ? $item->last_trained_at->format('M j, Y H:i') : '—';
                }),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('chatbase.admin.chatbots.create'), 'chatbase.settings');
    }

    public function bulkActions(): array
    {
        return [];
    }

    public function getBulkChanges(): array
    {
        return [];
    }

    public function getFilters(): array
    {
        return [];
    }

    public function saveBulkChangeItem($item, string $inputKey, ?string $inputValue): bool
    {
        return false;
    }

    public function isHasCustomFilter(): bool
    {
        return false;
    }

    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }
}
