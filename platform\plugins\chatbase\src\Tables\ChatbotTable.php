<?php

namespace Bo<PERSON>ble\Chatbase\Tables;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class ChatbotTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->addActions([
                EditAction::make()->route('chatbase.admin.chatbots.edit'),
                DeleteAction::make()->route('chatbase.admin.chatbots.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('store_name', function (ChatbaseAgent $item) {
                if ($item->store) {
                    try {
                        return Html::link(
                            route('marketplace.store.edit', $item->store->id),
                            $item->store->name,
                            ['target' => '_blank']
                        );
                    } catch (\Exception $e) {
                        return $item->store->name ?? "Store ID: {$item->store_id}";
                    }
                }
                return $item->store_id ? "Store ID: {$item->store_id}" : '—';
            })
            ->editColumn('customer_name', function (ChatbaseAgent $item) {
                if ($item->customer) {
                    try {
                        return Html::link(
                            route('customers.edit', $item->customer->id),
                            $item->customer->name,
                            ['target' => '_blank']
                        );
                    } catch (\Exception $e) {
                        return $item->customer->name ?? "Customer ID: {$item->customer_id}";
                    }
                }
                return $item->customer_id ? "Customer ID: {$item->customer_id}" : '—';
            })
            ->editColumn('chatbot_id', function (ChatbaseAgent $item) {
                return $item->chatbot_id ? Html::tag('code', $item->chatbot_id) : '—';
            })
            ->editColumn('status', function (ChatbaseAgent $item) {
                $statusLabels = [
                    'active' => ['success', trans('plugins/chatbase::chatbase.status.active')],
                    'inactive' => ['secondary', trans('plugins/chatbase::chatbase.status.inactive')],
                    'creating' => ['warning', trans('plugins/chatbase::chatbase.status.creating')],
                    'error' => ['danger', trans('plugins/chatbase::chatbase.status.error')],
                ];

                $status = $statusLabels[$item->status] ?? ['secondary', $item->status];

                return Html::tag('span', $status[1], [
                    'class' => 'badge bg-' . $status[0],
                ]);
            })
            ->editColumn('last_trained_at', function (ChatbaseAgent $item) {
                return $item->last_trained_at ? $item->last_trained_at->format('M j, Y H:i') : '—';
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'description',
                'store_id',
                'customer_id',
                'chatbot_id',
                'status',
                'created_at',
                'last_trained_at',
                'last_synced_at',
            ])
            ->with(['store', 'customer']);

        return $this->applyScopes($query);
    }
}
